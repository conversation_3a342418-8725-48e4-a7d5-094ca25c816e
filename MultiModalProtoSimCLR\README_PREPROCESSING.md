# 自动化PCAP数据预处理系统

## 概述

本系统提供了一个完整的自动化管道，能够直接处理原始PCAP文件并生成用于多模态网络入侵检测的训练数据。系统整合了现有的ProcessPCAP方法，并扩展了序列和图数据的提取功能。

## 核心特性

### 🚀 端到端自动化
- **一键处理**：从原始PCAP文件到训练就绪的多模态数据
- **智能分割**：自动进行训练/验证/测试集分割
- **错误处理**：自动跳过损坏文件，记录处理错误
- **进度跟踪**：实时显示处理进度和统计信息

### 🔄 整合现有方法
- **会话分割**：复用ProcessPCAP的会话分割功能
- **流量匿名化**：保持现有的匿名化策略
- **RGB生成**：集成现有的RGB图像生成方法
- **兼容性**：与现有ProtoSimCLR框架完全兼容

### 📊 多模态数据提取
- **序列数据**：提取用于Transformer的token序列
- **图数据**：构建用于GCN的网络图结构
- **图像数据**：生成用于ResNet的RGB图像
- **统一格式**：所有模态数据统一打包存储

### 🛡️ 质量保证
- **数据验证**：自动检查文件完整性和格式
- **质量控制**：过滤异常和低质量数据
- **统计报告**：生成详细的数据集统计信息
- **可视化**：提供数据分布的可视化图表

## 快速开始

### 1. 准备原始数据

将PCAP文件按类别组织：

```
raw_data/
├── normal/
│   ├── normal_001.pcap
│   ├── normal_002.pcap
│   └── ...
├── attack_type1/
│   ├── attack1_001.pcap
│   └── ...
└── attack_type2/
    └── ...
```

### 2. 运行自动化预处理

```bash
# 基本用法
python scripts/preprocess_data.py --input ./raw_data --output ./processed_data

# 完整端到端流程（包含训练）
python scripts/end_to_end_example.py --input ./raw_data --output ./experiment_results
```

### 3. 验证处理结果

```bash
# 验证数据质量
python scripts/validate_processed_data.py --data-dir ./processed_data
```

## 详细使用指南

### 命令行工具

#### 1. 数据预处理脚本

```bash
python scripts/preprocess_data.py [选项]

选项:
  --input, -i          原始PCAP数据目录 (必需)
  --output, -o         处理后数据输出目录 (必需)
  --config, -c         预处理配置文件路径
  --workers, -w        并行处理数量 (默认: 4)
  --temp, -t           临时文件目录
  --no-cleanup         不清理临时文件
  --train-ratio        训练集比例 (默认: 0.7)
  --val-ratio          验证集比例 (默认: 0.15)
  --test-ratio         测试集比例 (默认: 0.15)
  --max-sessions       每个PCAP最大会话数 (默认: 1000)
  --min-packets        每个会话最小包数 (默认: 10)
  --image-size         RGB图像尺寸 (默认: 32)
  --dry-run            只验证数据不实际处理
  --verbose, -v        详细输出
```

#### 2. 数据验证脚本

```bash
python scripts/validate_processed_data.py [选项]

选项:
  --data-dir           预处理后的数据目录 (必需)
  --output-dir         输出目录（报告和图表）
  --report-file        验证报告文件路径
  --no-viz             不生成可视化图表
```

#### 3. 端到端示例脚本

```bash
python scripts/end_to_end_example.py [选项]

选项:
  --input              原始PCAP数据目录
  --output             实验输出目录 (必需)
  --create-sample-data 创建示例数据用于演示
  --skip-preprocessing 跳过数据预处理
  --skip-training      跳过模型训练
  --skip-validation    跳过数据验证
  --skip-evaluation    跳过模型评估
  --stage1-epochs      对比学习轮数 (默认: 10)
  --stage2-epochs      元学习轮数 (默认: 20)
  --workers            预处理并行数量 (默认: 4)
  --gpu                GPU设备ID (默认: 0)
  --keep-temp          保留临时文件
```

### 配置文件

#### 预处理配置 (`config/preprocessing_config.yaml`)

```yaml
# 核心配置项
processing:
  max_sessions_per_pcap: 1000    # 每个PCAP最大会话数
  min_packets_per_session: 10    # 每个会话最小包数
  image_size: 32                 # RGB图像尺寸
  sequence_length: 512           # 序列最大长度
  graph_max_nodes: 100          # 图最大节点数

parallel:
  num_workers: 4                # 并行处理数量
  batch_size: 10                # 批处理大小

validation:
  check_corrupted_files: true   # 检查损坏文件
  min_file_size_bytes: 1024     # 最小文件大小
```

## 输出结构

### 处理后的数据结构

```
processed_data/
├── train/                     # 训练集
│   ├── class1/
│   │   ├── sample1_session_001.pkl    # 序列+图数据
│   │   ├── sample1_session_001.png    # RGB图像
│   │   └── ...
│   └── class2/
├── val/                       # 验证集
├── test/                      # 测试集
└── dataset_info.yaml         # 数据集统计信息
```

### 数据文件格式

#### PKL文件内容
```python
{
    'sequence': {
        'input_ids': [...],           # Token序列
        'attention_mask': [...],      # 注意力掩码
        'time_features': [...],       # 时间特征
        'raw_features': [...]         # 原始特征
    },
    'graph': {
        'adj_matrix': [...],          # 邻接矩阵
        'node_features': [...],       # 节点特征
        'global_features': [...]      # 全局特征
    },
    'sample_id': 'sample_name',       # 样本ID
    'class_name': 'class_name'        # 类别名称
}
```

#### PNG文件
- RGB图像，尺寸可配置（默认32x32）
- 基于网络包内容生成的可视化表示
- 保留协议和流量模式信息

## 处理流程详解

### 1. 数据验证阶段
- 检查PCAP文件完整性
- 验证文件大小和格式
- 过滤损坏或异常文件
- 按类别组织文件

### 2. 会话分割阶段
- 使用现有SessionSplitter
- 按时间窗口和协议分割会话
- 过滤小于最小包数的会话
- 限制每个PCAP的最大会话数

### 3. 流量匿名化阶段
- 使用现有TrafficAnonymizer
- 匿名化IP地址和敏感信息
- 保留网络拓扑结构
- 生成AL层数据

### 4. RGB图像生成阶段
- 使用现有RGBGenerator
- 将网络包转换为像素
- 应用协议相关的颜色映射
- 生成固定尺寸的RGB图像

### 5. 多模态特征提取阶段
- **序列特征**：提取包序列、时间戳、包大小等
- **图特征**：构建通信图、计算节点中心性等
- **图像特征**：已在前一阶段生成
- 统一打包为PKL格式

### 6. 数据集分割阶段
- 按配置比例分割训练/验证/测试集
- 保持类别平衡
- 生成数据集统计信息

## 性能优化

### 并行处理
- 支持多进程并行处理
- 可配置工作进程数量
- 自动负载均衡

### 内存管理
- 流式处理大文件
- 及时清理临时数据
- 可配置内存使用限制

### 错误恢复
- 自动跳过损坏文件
- 记录详细错误日志
- 支持断点续传

## 故障排除

### 常见问题

#### Q: 处理速度很慢怎么办？
A: 
- 增加并行工作进程数：`--workers 8`
- 减少每个PCAP的最大会话数：`--max-sessions 500`
- 使用SSD存储临时文件

#### Q: 内存不足怎么办？
A:
- 减少批处理大小
- 减少并行工作进程数
- 启用临时文件清理

#### Q: 某些PCAP文件处理失败？
A:
- 检查文件是否损坏
- 查看详细错误日志
- 使用`--dry-run`验证数据

#### Q: 生成的数据集不平衡？
A:
- 调整数据分割比例
- 检查原始数据分布
- 使用质量控制配置

### 日志文件
- `preprocessing.log`：主要处理日志
- `preprocessing_errors.log`：错误详情
- `dataset_info.yaml`：数据集统计

## 扩展开发

### 添加新的数据处理器
1. 在`data/`目录创建新的处理器类
2. 继承基础处理器接口
3. 在`automated_preprocessing.py`中集成

### 自定义特征提取
1. 修改`SequenceProcessor`或`GraphProcessor`
2. 添加新的特征提取方法
3. 更新数据格式文档

### 集成新的匿名化方法
1. 扩展`TrafficAnonymizer`
2. 添加新的匿名化策略
3. 更新配置选项

## 引用和致谢

本系统基于以下组件构建：
- ProcessPCAP：会话分割和匿名化
- ProtoSimCLR：原型网络和对比学习
- 多模态融合：注意力、门控、拼接策略

如果使用本系统，请引用相关论文和项目。
