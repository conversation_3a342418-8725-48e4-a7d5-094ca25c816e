"""
ResNet编码器 - 复用现有的SimCLR实现用于图像分支
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from typing import Dict, Optional


class ResNetEncoder(nn.Module):
    """
    基于ResNet的图像编码器，兼容SimCLR预训练权重
    """
    
    def __init__(self, config: Dict):
        """
        初始化ResNet编码器
        
        Args:
            config: 配置字典，包含：
                - base_model: 基础模型名称 ('resnet18', 'resnet50')
                - pretrained: 是否使用预训练权重
                - freeze_backbone: 是否冻结骨干网络
                - simclr_dim: SimCLR投影维度
                - output_dim: 输出特征维度
        """
        super(ResNetEncoder, self).__init__()
        
        # 从配置中提取参数
        self.base_model = config.get('base_model', 'resnet18')
        self.pretrained = config.get('pretrained', True)
        self.freeze_backbone = config.get('freeze_backbone', False)
        self.simclr_dim = config.get('simclr_dim', 128)
        self.output_dim = config.get('output_dim', 256)
        
        # 创建骨干网络
        self.backbone = self._create_backbone()
        
        # 获取特征维度
        self.feature_dim = self._get_feature_dim()
        
        # SimCLR投影头（如果需要）
        self.simclr_projection = nn.Sequential(
            nn.Linear(self.feature_dim, self.feature_dim),
            nn.ReLU(inplace=True),
            nn.Linear(self.feature_dim, self.simclr_dim)
        )
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(self.feature_dim, self.feature_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(self.feature_dim // 2, self.output_dim)
        )
        
        # 是否冻结骨干网络
        if self.freeze_backbone:
            self._freeze_backbone()
        
        print(f"✅ ResNet encoder created: {self.base_model}")
        print(f"✅ Feature dimension: {self.feature_dim}")
        print(f"✅ Output dimension: {self.output_dim}")
        print(f"✅ Backbone frozen: {self.freeze_backbone}")
    
    def _create_backbone(self) -> nn.Module:
        """
        创建ResNet骨干网络
        
        Returns:
            ResNet骨干网络
        """
        if self.base_model == "resnet18":
            if self.pretrained:
                backbone = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
            else:
                backbone = models.resnet18(weights=None)
        elif self.base_model == "resnet50":
            if self.pretrained:
                backbone = models.resnet50(weights=models.ResNet50_Weights.IMAGENET1K_V1)
            else:
                backbone = models.resnet50(weights=None)
        else:
            raise ValueError(f"Invalid backbone architecture: {self.base_model}")
        
        # 移除最后的全连接层
        backbone = nn.Sequential(*list(backbone.children())[:-1])
        
        return backbone
    
    def _get_feature_dim(self) -> int:
        """
        获取特征维度
        
        Returns:
            特征维度
        """
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            features = self.backbone(dummy_input)
            return features.view(features.size(0), -1).size(1)
    
    def _freeze_backbone(self):
        """
        冻结骨干网络参数
        """
        for param in self.backbone.parameters():
            param.requires_grad = False
        print("✅ Backbone parameters frozen")
    
    def unfreeze_backbone(self):
        """
        解冻骨干网络参数
        """
        for param in self.backbone.parameters():
            param.requires_grad = True
        self.freeze_backbone = False
        print("✅ Backbone parameters unfrozen")
    
    def forward(self, x: torch.Tensor, return_features: bool = False) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入图像 [batch_size, 3, H, W]
            return_features: 是否返回中间特征
            
        Returns:
            编码后的特征向量 [batch_size, output_dim]
            或包含中间特征的字典
        """
        # 特征提取
        features = self.backbone(x)  # [batch_size, feature_dim, 1, 1]
        features = features.view(features.size(0), -1)  # [batch_size, feature_dim]
        
        # 输出投影
        output = self.output_projection(features)
        
        if return_features:
            # SimCLR投影（用于对比学习）
            simclr_features = self.simclr_projection(features)
            
            return {
                'raw_features': features,
                'simclr_features': simclr_features,
                'output_features': output
            }
        else:
            return output
    
    def get_simclr_features(self, x: torch.Tensor) -> torch.Tensor:
        """
        获取SimCLR特征（用于对比学习）
        
        Args:
            x: 输入图像 [batch_size, 3, H, W]
            
        Returns:
            SimCLR特征 [batch_size, simclr_dim]
        """
        features = self.backbone(x)
        features = features.view(features.size(0), -1)
        simclr_features = self.simclr_projection(features)
        return simclr_features
    
    def get_raw_features(self, x: torch.Tensor) -> torch.Tensor:
        """
        获取原始特征（骨干网络输出）
        
        Args:
            x: 输入图像 [batch_size, 3, H, W]
            
        Returns:
            原始特征 [batch_size, feature_dim]
        """
        features = self.backbone(x)
        features = features.view(features.size(0), -1)
        return features
    
    def load_simclr_weights(self, checkpoint_path: str):
        """
        加载SimCLR预训练权重
        
        Args:
            checkpoint_path: 检查点文件路径
        """
        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            
            # 处理不同的保存格式
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            
            # 去除module.前缀
            new_state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
            
            # 只加载骨干网络权重
            backbone_state_dict = {}
            for k, v in new_state_dict.items():
                if k.startswith('backbone.'):
                    backbone_key = k.replace('backbone.', '')
                    backbone_state_dict[backbone_key] = v
            
            # 加载权重
            missing_keys, unexpected_keys = self.backbone.load_state_dict(backbone_state_dict, strict=False)
            
            print(f"✅ Loaded SimCLR weights from {checkpoint_path}")
            if missing_keys:
                print(f"⚠️ Missing keys: {missing_keys}")
            if unexpected_keys:
                print(f"⚠️ Unexpected keys: {unexpected_keys}")
                
        except Exception as e:
            print(f"❌ Error loading SimCLR weights: {e}")
            print("Using random initialization instead")
    
    def get_trainable_parameters(self):
        """
        获取可训练参数
        
        Returns:
            可训练参数列表
        """
        trainable_params = []
        
        if not self.freeze_backbone:
            trainable_params.extend(list(self.backbone.parameters()))
        
        trainable_params.extend(list(self.simclr_projection.parameters()))
        trainable_params.extend(list(self.output_projection.parameters()))
        
        return trainable_params
    
    def get_parameter_count(self) -> Dict[str, int]:
        """
        获取参数数量统计
        
        Returns:
            参数数量字典
        """
        backbone_params = sum(p.numel() for p in self.backbone.parameters())
        backbone_trainable = sum(p.numel() for p in self.backbone.parameters() if p.requires_grad)
        
        simclr_params = sum(p.numel() for p in self.simclr_projection.parameters())
        output_params = sum(p.numel() for p in self.output_projection.parameters())
        
        total_params = backbone_params + simclr_params + output_params
        total_trainable = backbone_trainable + simclr_params + output_params
        
        return {
            'backbone_total': backbone_params,
            'backbone_trainable': backbone_trainable,
            'simclr_projection': simclr_params,
            'output_projection': output_params,
            'total_parameters': total_params,
            'total_trainable': total_trainable
        }


class SimCLRResNet(nn.Module):
    """
    专门用于SimCLR对比学习的ResNet模型
    """
    
    def __init__(self, base_model: str = 'resnet18', out_dim: int = 128):
        """
        初始化SimCLR ResNet模型
        
        Args:
            base_model: 基础模型名称
            out_dim: 输出维度
        """
        super(SimCLRResNet, self).__init__()
        
        # 创建骨干网络
        if base_model == "resnet18":
            backbone = models.resnet18(weights=None)
        elif base_model == "resnet50":
            backbone = models.resnet50(weights=None)
        else:
            raise ValueError(f"Invalid backbone: {base_model}")
        
        # 获取特征维度
        dim_mlp = backbone.fc.in_features
        
        # 添加MLP投影头
        backbone.fc = nn.Sequential(
            nn.Linear(dim_mlp, dim_mlp),
            nn.ReLU(),
            nn.Linear(dim_mlp, out_dim)
        )
        
        self.backbone = backbone
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入图像
            
        Returns:
            投影后的特征
        """
        return self.backbone(x)


if __name__ == "__main__":
    # 测试代码
    config = {
        'base_model': 'resnet18',
        'pretrained': True,
        'freeze_backbone': False,
        'simclr_dim': 128,
        'output_dim': 256
    }
    
    encoder = ResNetEncoder(config)
    
    # 测试数据
    batch_size = 4
    x = torch.randn(batch_size, 3, 224, 224)
    
    # 前向传播
    output = encoder(x)
    print(f"Output shape: {output.shape}")
    
    # 获取特征
    features = encoder(x, return_features=True)
    print(f"Raw features shape: {features['raw_features'].shape}")
    print(f"SimCLR features shape: {features['simclr_features'].shape}")
    print(f"Output features shape: {features['output_features'].shape}")
    
    # 参数统计
    param_count = encoder.get_parameter_count()
    print(f"Parameter count: {param_count}")
