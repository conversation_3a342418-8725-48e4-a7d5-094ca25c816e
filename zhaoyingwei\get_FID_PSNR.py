import os
import numpy as np
import torch
from torchmetrics.image.fid import FrechetInceptionDistance
from torchmetrics.image.psnr import PeakSignalNoiseRatio
from PIL import Image
from tqdm import tqdm
import argparse


def load_images_from_dir(dir_path, max_images=None):
    """从目录加载图像并返回torch tensor"""
    image_paths = [os.path.join(dir_path, f) for f in os.listdir(dir_path)
                   if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    if max_images:
        image_paths = image_paths[:max_images]

    images = []
    for img_path in tqdm(image_paths, desc="Loading images"):
        img = Image.open(img_path).convert('RGB')
        img = np.array(img).transpose(2, 0, 1)  # HWC to CHW
        images.append(img)

    return torch.tensor(np.stack(images), dtype=torch.uint8)


def calculate_fid(real_images, fake_images):
    """计算FID分数"""
    fid = FrechetInceptionDistance(feature=2048)
    fid.update(real_images, real=True)
    fid.update(fake_images, real=False)
    return fid.compute().item()


def calculate_psnr(real_images, fake_images):
    """计算平均PSNR"""
    psnr = PeakSignalNoiseRatio(data_range=255.0)
    total_psnr = 0.0

    # 逐张计算PSNR（因为PSNR是逐像素比较的）
    for real, fake in zip(real_images, fake_images):
        # 添加batch维度
        real = real.unsqueeze(0).float()
        fake = fake.unsqueeze(0).float()
        total_psnr += psnr(fake, real)

    return total_psnr / len(real_images)


def main(real_dir, fake_dir, max_images=None):
    # 加载图像
    real_images = load_images_from_dir(real_dir, max_images)
    fake_images = load_images_from_dir(fake_dir, max_images)

    print(f"Loaded {len(real_images)} real images and {len(fake_images)} generated images")

    # 确保图像数量匹配
    min_images = min(len(real_images), len(fake_images))
    real_images = real_images[:min_images]
    fake_images = fake_images[:min_images]

    # 计算指标
    fid_score = calculate_fid(real_images, fake_images)
    psnr_score = calculate_psnr(real_images, fake_images)

    print("\nEvaluation Results:")
    print(f"FID (lower is better): {fid_score:.2f}")
    print(f"PSNR (higher is better): {psnr_score:.2f} dB")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Evaluate generated images against real images')
    parser.add_argument('--r', type=str, default= "./Htbot", required=True, help='Directory containing real images')
    parser.add_argument('--f', type=str, default= "./imgs", required=True, help='Directory containing generated images')
    parser.add_argument('--max_images', type=int, default=None, help='Maximum number of images to evaluate')

    args = parser.parse_args()

    # 检查目录是否存在
    if not os.path.exists(args.r):
        raise ValueError(f"Real images directory does not exist: {args.real_dir}")
    if not os.path.exists(args.f):
        raise ValueError(f"Generated images directory does not exist: {args.fake_dir}")

    main(args.r, args.f, args.max_images)
