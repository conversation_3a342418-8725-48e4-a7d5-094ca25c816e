# 扁平文件结构自动化预处理系统

## 🎯 系统概述

本系统专门为**扁平PCAP文件结构**设计，能够自动处理每个PCAP文件代表一个完整类别的数据集。系统会从每个PCAP文件中提取多个网络会话，并在会话级别进行训练/验证/测试集分割。

## 📁 输入数据结构

### 实际数据结构
```
raw_data/
├── normal.pcap          # 正常流量（包含多个会话）
├── ddos.pcap           # DDoS攻击（包含多个会话）
├── port_scan.pcap      # 端口扫描（包含多个会话）
├── malware.pcap        # 恶意软件（包含多个会话）
├── botnet.pcap         # 僵尸网络（包含多个会话）
└── ...
```

### 关键特点
- ✅ **扁平结构**：所有PCAP文件直接在根目录下
- ✅ **文件名即类别**：文件名（不含扩展名）作为类别标签
- ✅ **一文件一类别**：每个PCAP文件代表一个完整的攻击类型或流量类别
- ✅ **多会话包含**：每个PCAP文件包含该类别的多个网络会话/流

## 🔄 处理流程

### 1. 会话提取阶段
```
normal.pcap → 提取 → normal_session_001, normal_session_002, ...
ddos.pcap   → 提取 → ddos_session_001, ddos_session_002, ...
...
```

### 2. 会话级别分割
```
所有会话 → 随机打乱 → 按比例分割
├── 70% → 训练集（包含所有类别的会话）
├── 15% → 验证集（包含所有类别的会话）
└── 15% → 测试集（包含所有类别的会话）
```

### 3. 多模态数据生成
每个会话生成：
- **序列数据**：用于Transformer分支
- **图数据**：用于GCN分支  
- **RGB图像**：用于ResNet分支

## 📊 输出数据结构

```
processed_data/
├── train/                           # 训练集
│   ├── normal/
│   │   ├── normal_session_001.pkl   # 序列+图数据
│   │   ├── normal_session_001.png   # RGB图像
│   │   ├── normal_session_015.pkl
│   │   ├── normal_session_015.png
│   │   └── ...
│   ├── ddos/
│   │   ├── ddos_session_003.pkl
│   │   ├── ddos_session_003.png
│   │   └── ...
│   └── ...
├── val/                             # 验证集
│   ├── normal/
│   ├── ddos/
│   └── ...
├── test/                            # 测试集
│   ├── normal/
│   ├── ddos/
│   └── ...
└── dataset_info.yaml               # 数据集统计信息
```

## 🚀 使用方法

### 1. 基本用法
```bash
# 安装依赖
pip install scapy torch torchvision scikit-learn PyYAML tqdm

# 运行预处理
cd MultiModalProtoSimCLR
python scripts/preprocess_data.py --input ../raw_data --output ./processed_data
```

### 2. 自定义参数
```bash
python scripts/preprocess_data.py \
    --input ../raw_data \
    --output ./processed_data \
    --workers 8 \
    --max-sessions 500 \
    --image-size 64 \
    --train-ratio 0.8 \
    --val-ratio 0.1 \
    --test-ratio 0.1
```

### 3. 端到端流程
```bash
# 从PCAP到训练完成的完整流程
python scripts/end_to_end_example.py \
    --input ../raw_data \
    --output ./experiment_results
```

### 4. 数据验证
```bash
# 验证处理后的数据质量
python scripts/validate_processed_data.py \
    --data-dir ./processed_data \
    --output-dir ./validation_results
```

## ⚙️ 配置选项

### 主要配置参数
```yaml
# 数据处理配置
processing:
  max_sessions_per_pcap: 1000    # 每个PCAP最大会话数
  min_packets_per_session: 10    # 每个会话最小包数
  image_size: 32                 # RGB图像尺寸
  sequence_length: 512           # 序列最大长度
  graph_max_nodes: 100          # 图最大节点数

# 数据分割配置
output:
  train_ratio: 0.7              # 训练集比例
  val_ratio: 0.15               # 验证集比例
  test_ratio: 0.15              # 测试集比例

# 并行处理配置
parallel:
  num_workers: 4                # 并行处理数量
  batch_size: 10                # 批处理大小
```

## 🔍 数据质量保证

### 自动验证
- ✅ PCAP文件完整性检查
- ✅ 会话质量过滤（最小包数）
- ✅ 数据格式验证
- ✅ 类别平衡检查

### 错误处理
- ✅ 自动跳过损坏文件
- ✅ 详细错误日志记录
- ✅ 处理进度实时显示
- ✅ 异常恢复机制

## 📈 处理统计示例

```yaml
# dataset_info.yaml 示例
total_samples: 2847
splits:
  train:
    total_samples: 1993
    classes:
      normal: 456
      ddos: 523
      port_scan: 387
      malware: 412
      botnet: 215
  val:
    total_samples: 427
    classes:
      normal: 89
      ddos: 95
      port_scan: 78
      malware: 91
      botnet: 74
  test:
    total_samples: 427
    classes:
      normal: 88
      ddos: 96
      port_scan: 79
      malware: 90
      botnet: 74
```

## 🛠️ 技术特点

### 核心优势
1. **会话级别分割**：确保每个分割都包含所有类别的样本
2. **自动化处理**：无需手动干预，一键完成所有预处理
3. **质量控制**：多层次的数据验证和错误处理
4. **可扩展性**：支持任意数量的攻击类别
5. **兼容性**：与现有ProtoSimCLR框架完全兼容

### 处理能力
- 支持大型PCAP文件（自动分块处理）
- 并行处理加速
- 内存优化管理
- 断点续传支持

## 🔧 故障排除

### 常见问题

#### Q: 某个PCAP文件处理失败？
A: 检查文件是否损坏，查看错误日志获取详细信息

#### Q: 生成的会话数量不平衡？
A: 这是正常的，因为不同攻击类型的PCAP文件包含的会话数量可能不同

#### Q: 处理速度慢？
A: 增加并行工作进程数：`--workers 8`

#### Q: 内存不足？
A: 减少批处理大小和并行数量

### 日志文件
- `preprocessing.log`：主要处理日志
- `preprocessing_errors.log`：错误详情
- `dataset_info.yaml`：数据集统计

## 🎯 下一步

1. **运行预处理**：处理你的扁平PCAP数据
2. **验证数据**：检查生成的多模态数据质量
3. **开始训练**：使用处理后的数据训练多模态模型
4. **评估性能**：在测试集上评估模型效果

## 📞 技术支持

如果遇到问题：
1. 查看详细日志文件
2. 运行测试脚本验证功能：`python test_flat_structure.py`
3. 使用`--dry-run`模式验证数据而不实际处理
4. 检查PCAP文件格式和完整性

---

**注意**：本系统专门为扁平文件结构设计，如果你的数据是层次结构（每个类别有单独目录），请使用标准的预处理管道。
