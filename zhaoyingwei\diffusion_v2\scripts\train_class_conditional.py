"""
Train a class-conditional diffusion model with Classifier-Free Guidance support.
This script is specifically designed for ImageFolder datasets with class labels.
"""

import argparse
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from diffusion_v2 import dist_util, logger
from diffusion_v2.image_datasets import load_data
from diffusion_v2.resample import create_named_schedule_sampler
from diffusion_v2.script_util import (
    model_and_diffusion_defaults,
    create_model_and_diffusion,
    args_to_dict,
    add_dict_to_argparser,
)
from diffusion_v2.train_util import TrainLoop


def main():
    args = create_argparser().parse_args()

    dist_util.setup_dist()
    
    # Configure logger with custom directory if specified
    if args.log_dir:
        os.makedirs(args.log_dir, exist_ok=True)
        logger.configure(dir=args.log_dir)
    else:
        logger.configure()

    logger.log("creating model and diffusion...")
    logger.log(f"Training class-conditional model with {args.num_classes} classes")
    logger.log(f"CFG dropout probability: {args.cfg_dropout_prob}")
    
    model, diffusion = create_model_and_diffusion(
        **args_to_dict(args, model_and_diffusion_defaults().keys())
    )
    model.to(dist_util.dev())
    schedule_sampler = create_named_schedule_sampler(args.schedule_sampler, diffusion)

    logger.log("creating data loader...")
    logger.log(f"Loading data from: {args.data_dir}")
    logger.log(f"Using ImageFolder format: {args.use_imagefolder}")
    
    data = load_data(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        image_size=args.image_size,
        class_cond=args.class_cond,
        use_imagefolder=args.use_imagefolder,
    )

    logger.log("training...")
    TrainLoop(
        model=model,
        diffusion=diffusion,
        data=data,
        batch_size=args.batch_size,
        microbatch=args.microbatch,
        lr=args.lr,
        ema_rate=args.ema_rate,
        log_interval=args.log_interval,
        save_interval=args.save_interval,
        resume_checkpoint=args.resume_checkpoint,
        use_fp16=args.use_fp16,
        fp16_scale_growth=args.fp16_scale_growth,
        schedule_sampler=schedule_sampler,
        weight_decay=args.weight_decay,
        lr_anneal_steps=args.lr_anneal_steps,
        log_dir=args.log_dir,
        cfg_dropout_prob=args.cfg_dropout_prob,
    ).run_loop()


def create_argparser():
    defaults = dict(
        # Data settings
        data_dir="",
        use_imagefolder=True,  # Use ImageFolder format for class labels
        
        # Training settings
        schedule_sampler="uniform",
        lr=1e-4,
        weight_decay=0.0,
        lr_anneal_steps=0,
        batch_size=16,
        microbatch=-1,  # -1 disables microbatches
        ema_rate="0.9999",  # comma-separated list of EMA values
        log_interval=10,
        save_interval=1000,
        resume_checkpoint="",
        use_fp16=False,
        fp16_scale_growth=1e-3,
        
        # CFG settings
        cfg_dropout_prob=0.1,  # Classifier-Free Guidance dropout probability
        
        # Output settings
        log_dir="",  # Custom log directory for weights and logs
        
        # Model settings (will be overridden by model_and_diffusion_defaults)
        class_cond=True,  # Enable class conditioning
        num_classes=10,   # Number of classes (should match your dataset)
    )
    
    # Add model and diffusion defaults
    defaults.update(model_and_diffusion_defaults())
    
    # Override some defaults for class-conditional training
    defaults.update(dict(
        class_cond=True,  # Force class conditioning
        learn_sigma=False,  # Typically False for class-conditional models
        diffusion_steps=1000,  # Standard number of diffusion steps
        noise_schedule="linear",  # Linear noise schedule
    ))
    
    parser = argparse.ArgumentParser()
    add_dict_to_argparser(parser, defaults)
    
    # Add some helpful descriptions
    parser.add_argument(
        "--data_dir", 
        help="Path to ImageFolder dataset directory"
    )
    parser.add_argument(
        "--num_classes", 
        type=int,
        help="Number of classes in the dataset"
    )
    parser.add_argument(
        "--cfg_dropout_prob", 
        type=float,
        help="Probability of dropping class labels for CFG training (0.0-1.0)"
    )
    parser.add_argument(
        "--log_dir", 
        help="Directory to save model checkpoints and logs"
    )
    parser.add_argument(
        "--guidance_scale", 
        type=float, 
        default=1.0,
        help="Guidance scale for sampling (not used in training)"
    )
    
    return parser


if __name__ == "__main__":
    main()
