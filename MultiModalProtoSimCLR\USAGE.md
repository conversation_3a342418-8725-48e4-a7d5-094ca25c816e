# 多视图三分支对比元学习系统使用指南

## 系统概述

本系统基于现有的ProtoSimCLR框架，实现了一个多模态三分支网络架构，用于网络入侵检测的少样本学习任务。

### 核心特性

- **三分支架构**：序列分支(Transformer) + 图像分支(ResNet) + 图分支(GCN)
- **多种融合策略**：注意力融合、门控融合、拼接融合
- **两阶段训练**：对比学习预训练 + 元学习微调
- **模块化设计**：易于扩展和实验

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install torch torchvision torch-geometric
pip install transformers scikit-learn PyYAML
pip install tqdm matplotlib seaborn tensorboard
pip install scapy networkx pillow
```

### 2. 数据准备

#### 原始PCAP数据结构

将你的原始PCAP文件按以下**扁平结构**组织：

```
raw_data/
├── normal.pcap          # 正常流量类别
├── ddos.pcap           # DDoS攻击类别
├── port_scan.pcap      # 端口扫描类别
├── malware.pcap        # 恶意软件类别
└── ...
```

**重要说明：**
- 每个PCAP文件代表一个完整的攻击类别或流量类型
- 文件名（不含扩展名）将作为类别标签
- 每个PCAP文件包含该类别的多个网络会话/流
- 系统会自动从每个PCAP文件中提取多个会话作为训练样本

#### 自动化数据预处理

使用自动化预处理管道直接处理原始PCAP文件：

```bash
# 基本用法 - 自动处理PCAP文件生成多模态数据
python scripts/preprocess_data.py --input ./raw_data --output ./processed_data

# 使用自定义配置
python scripts/preprocess_data.py \
    --input ./raw_data \
    --output ./processed_data \
    --config config/preprocessing_config.yaml

# 调整处理参数
python scripts/preprocess_data.py \
    --input ./raw_data \
    --output ./processed_data \
    --workers 8 \
    --image-size 64 \
    --max-sessions 500

# 保留临时文件用于调试
python scripts/preprocess_data.py \
    --input ./raw_data \
    --output ./processed_data \
    --no-cleanup

# 只验证数据不实际处理
python scripts/preprocess_data.py \
    --input ./raw_data \
    --output ./processed_data \
    --dry-run
```

#### 处理后的数据结构

预处理完成后，数据将自动组织为以下结构：

```
processed_data/
├── train/                           # 训练集（70%的会话）
│   ├── normal/
│   │   ├── normal_session_001.pkl   # 序列和图数据
│   │   ├── normal_session_001.png   # RGB图像
│   │   ├── normal_session_015.pkl
│   │   ├── normal_session_015.png
│   │   └── ...
│   ├── ddos/
│   │   ├── ddos_session_003.pkl
│   │   ├── ddos_session_003.png
│   │   └── ...
│   └── port_scan/
│       └── ...
├── val/                             # 验证集（15%的会话）
│   ├── normal/
│   ├── ddos/
│   └── ...
├── test/                            # 测试集（15%的会话）
│   ├── normal/
│   ├── ddos/
│   └── ...
└── dataset_info.yaml               # 数据集统计信息
```

**数据分割说明：**
- 分割在**会话级别**进行，不是文件级别
- 每个分割（train/val/test）都包含所有类别的样本
- 确保类别分布在各个分割中保持平衡

### 3. 数据验证

验证预处理后的数据质量：

```bash
# 验证数据完整性和格式
python scripts/validate_processed_data.py --data-dir ./processed_data

# 生成详细报告和可视化
python scripts/validate_processed_data.py \
    --data-dir ./processed_data \
    --output-dir ./validation_results \
    --report-file ./validation_report.md
```

### 4. 端到端自动化流程

对于完整的端到端处理，可以使用自动化脚本：

```bash
# 完整端到端流程（从PCAP到训练完成）
python scripts/end_to_end_example.py \
    --input ./raw_data \
    --output ./experiment_results

# 创建示例数据并运行完整流程（用于演示）
python scripts/end_to_end_example.py \
    --create-sample-data \
    --output ./demo_experiment

# 自定义训练参数
python scripts/end_to_end_example.py \
    --input ./raw_data \
    --output ./experiment_results \
    --stage1-epochs 50 \
    --stage2-epochs 100 \
    --workers 8 \
    --gpu 0
```

### 5. 手动训练模型

如果已有预处理好的数据，可以直接训练：

```bash
# 完整两阶段训练
python experiments/train_multimodal.py --config config/multimodal_config.yaml

# 只运行对比学习预训练
python experiments/train_multimodal.py --config config/multimodal_config.yaml --stage stage1

# 只运行元学习微调
python experiments/train_multimodal.py --config config/multimodal_config.yaml --stage stage2

# 从检查点恢复训练
python experiments/train_multimodal.py \
    --config config/multimodal_config.yaml \
    --resume checkpoints/stage1_epoch_50.pth
```

### 6. 评估模型

```bash
# 基本评估
python experiments/evaluate_multimodal.py \
    --config config/multimodal_config.yaml \
    --checkpoint checkpoints/stage2_best.pth \
    --test-data processed_data/test \
    --episodes 100

# 详细评估（包含分支贡献分析）
python experiments/evaluate_multimodal.py \
    --config config/multimodal_config.yaml \
    --checkpoint checkpoints/stage2_best.pth \
    --test-data processed_data/test \
    --episodes 200 \
    --output-dir ./evaluation_results
```

## 配置说明

### 预处理配置参数

预处理配置文件 `config/preprocessing_config.yaml`：

```yaml
# 输入输出配置
input:
  raw_pcap_dir: "./raw_data"
  supported_extensions: [".pcap", ".pcapng"]

output:
  processed_data_dir: "./processed_data"
  train_ratio: 0.7           # 训练集比例
  val_ratio: 0.15            # 验证集比例
  test_ratio: 0.15           # 测试集比例

# 数据处理配置
processing:
  max_sessions_per_pcap: 1000    # 每个PCAP最大会话数
  min_packets_per_session: 10    # 每个会话最小包数
  image_size: 32                 # RGB图像尺寸
  sequence_length: 512           # 序列最大长度
  graph_max_nodes: 100          # 图最大节点数

# 并行处理配置
parallel:
  num_workers: 4                # 并行处理数量
  batch_size: 10                # 批处理大小

# 质量控制配置
validation:
  check_corrupted_files: true   # 检查损坏文件
  min_file_size_bytes: 1024     # 最小文件大小
```

### 训练配置参数

训练配置文件 `config/multimodal_config.yaml`：

```yaml
# 数据配置
data:
  train_dataset: "./processed_data/train"
  val_dataset: "./processed_data/val"
  test_dataset: "./processed_data/test"
  sequence_length: 512        # 序列最大长度
  graph_max_nodes: 100       # 图最大节点数
  img_size: 32               # 图像尺寸

# 模型配置
model:
  sequence_branch:           # Transformer配置
    embedding_dim: 256
    num_heads: 8
    num_layers: 6

  image_branch:              # ResNet配置
    base_model: "resnet18"
    output_dim: 256

  graph_branch:              # GCN配置
    hidden_dims: [128, 256]
    output_dim: 256

  fusion:                    # 融合配置
    strategy: "attention"    # attention/gate/concat
    output_dim: 512

# 训练配置
training:
  stage1_epochs: 100         # 对比学习轮数
  stage2_epochs: 200         # 元学习轮数
  learning_rate: 0.001
  device: "cuda:0"           # 计算设备
```

## 模块说明

### 1. 数据处理模块 (`data/`)

- **PCAPProcessor**: 从PCAP文件提取序列和图数据
- **SequenceProcessor**: 处理序列数据用于Transformer
- **GraphProcessor**: 处理图数据用于GCN
- **MultiModalDataset**: 多模态数据集加载器

### 2. 模型模块 (`models/`)

#### 编码器 (`encoders/`)
- **TransformerEncoder**: 序列数据编码器
- **GCNEncoder**: 图数据编码器  
- **ResNetEncoder**: 图像数据编码器

#### 融合模块 (`fusion/`)
- **AttentionFusion**: 基于注意力的融合
- **GateFusion**: 基于门控的融合
- **ConcatFusion**: 基于拼接的融合

#### 主模型
- **MultiModalProtoNet**: 多模态原型网络

### 3. 训练模块 (`training/`)

- **MultiModalTrainer**: 两阶段训练器
- **ContrastiveTrainer**: 对比学习训练器

## 实验和分析

### 1. 测试系统功能

```bash
python test_multimodal_system.py
```

### 2. 消融实验

修改配置文件中的融合策略：

```yaml
model:
  fusion:
    strategy: "attention"  # 改为 "gate" 或 "concat"
```

### 3. 分支贡献分析

```python
# 获取各分支特征
branch_features = model.get_individual_branch_features(data)

# 融合分析
analysis = model.get_fusion_analysis(data)
print(analysis['branch_similarities'])
print(analysis['fusion_weights'])
```

### 4. 参数冻结实验

```python
# 冻结特定分支
model.freeze_branch('image')  # 冻结图像分支
model.unfreeze_branch('image')  # 解冻图像分支
```

## 性能优化建议

### 1. 内存优化

- 减少batch_size
- 使用梯度累积
- 启用混合精度训练

### 2. 训练加速

- 使用多GPU训练
- 预训练权重初始化
- 学习率调度优化

### 3. 模型调优

- 调整融合策略
- 优化各分支输出维度
- 实验不同的损失权重

## 常见问题

### Q1: 内存不足怎么办？

A: 减少batch_size，或者减少序列长度和图节点数：

```yaml
data:
  sequence_length: 256  # 从512减少到256
  graph_max_nodes: 50   # 从100减少到50
```

### Q2: 如何添加新的融合策略？

A: 在`models/fusion/`目录下创建新的融合模块，继承基类并实现forward方法。

### Q3: 如何使用预训练的SimCLR权重？

A: 在ResNetEncoder中调用load_simclr_weights方法：

```python
image_encoder.load_simclr_weights('path/to/simclr_checkpoint.pth')
```

### Q4: 训练中断如何恢复？

A: 使用--resume参数：

```bash
python experiments/train_multimodal.py \
    --config config/multimodal_config.yaml \
    --resume checkpoints/stage1_epoch_50.pth
```

## 扩展开发

### 添加新的模态

1. 在`data/`中创建新的处理器
2. 在`models/encoders/`中创建新的编码器
3. 修改融合模块以支持新模态
4. 更新配置文件

### 自定义损失函数

在`MultiModalProtoNet`中添加新的损失计算方法，并在训练器中调用。

### 集成新的评估指标

在`evaluate_multimodal.py`中添加新的评估函数。

## 引用

如果使用本系统，请引用相关论文：

```bibtex
@article{multimodal_protonet,
  title={Multi-Modal Prototypical Networks with Contrastive Learning for Few-Shot Network Intrusion Detection},
  author={Your Name},
  journal={Your Journal},
  year={2024}
}
```
