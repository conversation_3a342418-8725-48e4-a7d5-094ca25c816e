# 🚀 快速开始指南

## 5分钟上手ResNet分类

### 步骤1: 环境准备
```bash
# 安装依赖
pip install torch torchvision tqdm prettytable scikit-learn matplotlib pillow
```

### 步骤2: 准备数据
```bash
# 创建数据目录结构
mkdir -p datasets/my_dataset/{train,val}/{class0,class1,class2}

# 将图像放入对应目录
# datasets/my_dataset/train/class0/ - 类别0的训练图像
# datasets/my_dataset/train/class1/ - 类别1的训练图像
# datasets/my_dataset/val/class0/   - 类别0的验证图像
# datasets/my_dataset/val/class1/   - 类别1的验证图像
```

### 步骤3: 修改配置
```python
# 编辑 Renet_new/renet_train.py
# 第37行修改数据路径
image_path = os.path.join(data_root, "datasets", "my_dataset")

# 第98行修改类别数
net = resnet34(num_classes=3)  # 改为你的类别数

# 第50行修改批次大小（可选）
batch_size = 16
```

### 步骤4: 开始训练
```bash
cd Renet_new
python renet_train.py
```

### 步骤5: 测试模型
```python
# 编辑 Renet_new/renet_predict.py
# 第131行修改类别数
model = resnet34(num_classes=3).to(device)

# 第139行修改模型路径
weights_path = "./resnet-my_dataset-0.pth"
```

```bash
python renet_predict.py
```

## 10分钟上手扩散模型

### 步骤1: 准备环境
```bash
pip install torch torchvision blobfile mpi4py
```

### 步骤2: 准备图像数据
```bash
# 创建图像目录
mkdir -p datasets/my_images

# 将所有训练图像放入该目录（支持jpg、png格式）
# 建议图像尺寸一致，如64x64或128x128
```

### 步骤3: 开始训练
```bash
cd improved_diffusion/scripts

# 基础训练命令
python image_train.py \
    --data_dir ../../datasets/my_images \
    --image_size 64 \
    --num_channels 128 \
    --diffusion_steps 1000 \
    --lr 1e-4 \
    --batch_size 8 \
    --save_interval 5000
```

### 步骤4: 生成图像
```bash
# 等待训练完成后，使用保存的模型生成图像
python image_sample.py \
    --model_path ./model005000.pt \
    --num_samples 50 \
    --batch_size 5 \
    --image_size 64
```

## 🔧 常用配置模板

### ResNet小数据集配置
```python
# 适用于数据量 < 10K
batch_size = 16
epochs = 100
lr = 0.001
model = resnet34(num_classes=your_classes)
```

### ResNet大数据集配置
```python
# 适用于数据量 > 100K
batch_size = 64
epochs = 50
lr = 0.0001
model = resnet50(num_classes=your_classes)
```

### 扩散模型快速训练配置
```bash
# 快速验证效果（低质量）
--image_size 32 --num_channels 64 --diffusion_steps 500

# 标准配置（中等质量）
--image_size 64 --num_channels 128 --diffusion_steps 1000

# 高质量配置（需要更多时间）
--image_size 128 --num_channels 192 --diffusion_steps 1000
```

## ⚡ 性能优化技巧

### GPU内存不足解决方案
```python
# ResNet: 减少批次大小
batch_size = 8  # 或更小

# 扩散模型: 使用梯度检查点
--use_checkpoint True

# 使用混合精度训练
--use_fp16 True
```

### 加速训练技巧
```python
# 使用预训练模型
net.load_state_dict(torch.load("pretrained_model.pth"))

# 冻结早期层，只训练后面的层
for param in net.layer1.parameters():
    param.requires_grad = False
```

### 提升生成质量
```bash
# 使用更多采样步数
--timestep_respacing "250"  # 250步采样

# 使用EMA权重
--ema_rate "0.9999"
```

## 📊 快速验证检查清单

### ResNet训练检查
- [ ] 数据路径正确
- [ ] 类别数设置正确
- [ ] 训练损失下降
- [ ] 验证准确率提升
- [ ] 模型文件正常保存

### 扩散模型检查
- [ ] 图像数据格式正确
- [ ] 训练损失稳定下降
- [ ] 生成图像质量逐步提升
- [ ] 检查点文件正常保存
- [ ] 采样脚本能正常运行

## 🆘 常见错误解决

### 1. 路径错误
```
FileNotFoundError: [Errno 2] No such file or directory
```
**解决**: 检查数据路径是否正确，确保目录存在

### 2. 内存不足
```
RuntimeError: CUDA out of memory
```
**解决**: 减少batch_size，或使用--use_fp16

### 3. 类别数不匹配
```
RuntimeError: size mismatch for fc.weight
```
**解决**: 确保模型的num_classes与数据集类别数一致

### 4. 权重文件不存在
```
FileNotFoundError: file 'model.pth' does not exist
```
**解决**: 检查模型文件路径，确保训练已完成并保存

## 📞 获取帮助

如果遇到问题：
1. 检查错误信息和上述常见错误解决方案
2. 确认环境配置和依赖安装
3. 验证数据格式和路径设置
4. 尝试使用更小的数据集进行测试

---

**提示**: 建议先用小数据集测试整个流程，确认无误后再使用完整数据集训练。
