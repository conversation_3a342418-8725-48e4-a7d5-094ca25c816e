"""
Train a classifier for Classifier Guidance.
This classifier is trained on noisy images at different timesteps.
"""

import argparse
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np

from diffusion_v2 import dist_util, logger
from diffusion_v2.image_datasets import load_data
from diffusion_v2.classifier import Classifier
from diffusion_v2.gaussian_diffusion import GaussianDiffusion, ModelMeanType, ModelVarType, LossType
from diffusion_v2.script_util import (
    model_and_diffusion_defaults,
    classifier_defaults,
    create_classifier,
    add_dict_to_argparser,
    args_to_dict,
)


def main():
    args = create_argparser().parse_args()

    dist_util.setup_dist()
    
    # Configure logger with custom directory if specified
    if args.log_dir:
        os.makedirs(args.log_dir, exist_ok=True)
        logger.configure(dir=args.log_dir)
    else:
        logger.configure()

    logger.log("creating classifier...")
    classifier = create_classifier(**args_to_dict(args, classifier_defaults().keys()))
    classifier.to(dist_util.dev())
    
    logger.log("creating diffusion process for noisy training...")
    # Create diffusion process to add noise to training images
    betas = np.linspace(args.beta_start, args.beta_end, args.diffusion_steps)
    diffusion = GaussianDiffusion(
        betas=betas,
        model_mean_type=ModelMeanType.EPSILON,
        model_var_type=ModelVarType.FIXED_SMALL,
        loss_type=LossType.MSE,
    )

    logger.log("creating data loader...")
    data = load_data(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        image_size=args.image_size,
        class_cond=True,  # Always need class labels for classifier training
        use_imagefolder=args.use_imagefolder,
    )

    logger.log("training classifier...")
    train_classifier(
        classifier=classifier,
        diffusion=diffusion,
        data=data,
        args=args,
    )


def train_classifier(classifier, diffusion, data, args):
    """Train the classifier on noisy images."""
    
    # Setup optimizer
    optimizer = torch.optim.AdamW(
        classifier.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay,
    )
    
    # Setup learning rate scheduler
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=args.iterations, eta_min=args.lr * 0.01
    )
    
    step = 0
    best_accuracy = 0.0
    
    logger.log(f"Training for {args.iterations} iterations...")
    
    while step < args.iterations:
        for batch in data:
            if step >= args.iterations:
                break
                
            images, labels_dict = batch
            images = images.to(dist_util.dev())
            labels = labels_dict["y"].to(dist_util.dev())
            
            # Sample random timesteps
            t = torch.randint(0, diffusion.num_timesteps, (images.shape[0],), device=dist_util.dev())
            
            # Add noise to images
            noise = torch.randn_like(images)
            noisy_images = diffusion.q_sample(images, t, noise=noise)
            
            # Forward pass
            logits = classifier(noisy_images, t)
            loss = F.cross_entropy(logits, labels)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            
            # Gradient clipping
            if args.grad_clip > 0:
                torch.nn.utils.clip_grad_norm_(classifier.parameters(), args.grad_clip)
            
            optimizer.step()
            scheduler.step()
            
            # Logging
            if step % args.log_interval == 0:
                # Calculate accuracy
                with torch.no_grad():
                    predictions = torch.argmax(logits, dim=1)
                    accuracy = (predictions == labels).float().mean().item()
                
                logger.logkv("step", step)
                logger.logkv("loss", loss.item())
                logger.logkv("accuracy", accuracy)
                logger.logkv("lr", scheduler.get_last_lr()[0])
                logger.dumpkvs()
                
                if accuracy > best_accuracy:
                    best_accuracy = accuracy
                    logger.log(f"New best accuracy: {best_accuracy:.4f}")
            
            # Save checkpoint
            if step % args.save_interval == 0 and step > 0:
                save_checkpoint(classifier, optimizer, step, args)
            
            step += 1
    
    # Final save
    save_checkpoint(classifier, optimizer, step, args, is_final=True)
    logger.log(f"Training completed. Best accuracy: {best_accuracy:.4f}")


def save_checkpoint(classifier, optimizer, step, args, is_final=False):
    """Save classifier checkpoint."""
    if dist_util.get_rank() == 0:
        checkpoint = {
            'model_state_dict': classifier.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'step': step,
            'args': args,
        }
        
        if is_final:
            filename = "classifier_final.pt"
        else:
            filename = f"classifier_{step:06d}.pt"
        
        save_path = os.path.join(args.log_dir if args.log_dir else ".", filename)
        torch.save(checkpoint, save_path)
        logger.log(f"Saved checkpoint: {save_path}")


def create_argparser():
    defaults = dict(
        # Data settings
        data_dir="",
        use_imagefolder=True,
        
        # Training settings
        iterations=100000,
        lr=3e-4,
        weight_decay=0.05,
        batch_size=32,
        grad_clip=1.0,
        log_interval=100,
        save_interval=10000,
        
        # Diffusion settings for noisy training
        diffusion_steps=1000,
        beta_start=0.0001,
        beta_end=0.02,
        
        # Output settings
        log_dir="",
    )
    
    # Add classifier defaults
    defaults.update(classifier_defaults())
    
    parser = argparse.ArgumentParser()
    add_dict_to_argparser(parser, defaults)
    
    # Add helpful descriptions
    parser.add_argument(
        "--data_dir", 
        help="Path to ImageFolder dataset directory"
    )
    parser.add_argument(
        "--num_classes", 
        type=int,
        help="Number of classes in the dataset"
    )
    parser.add_argument(
        "--log_dir", 
        help="Directory to save classifier checkpoints and logs"
    )
    
    return parser


if __name__ == "__main__":
    main()
