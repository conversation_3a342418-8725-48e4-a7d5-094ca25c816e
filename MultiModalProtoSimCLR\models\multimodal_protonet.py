"""
多模态原型网络 - 整合三个分支的多模态ProtoNet实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple

from .encoders.transformer_encoder import TransformerEncoder
from .encoders.gcn_encoder import GCNEncoder
from .encoders.resnet_encoder import ResNetEncoder
from .fusion.attention_fusion import AttentionFusion
from .fusion.gate_fusion import GateFusion
from .fusion.concat_fusion import ConcatFusion


class MultiModalProtoNet(nn.Module):
    """
    多模态原型网络，整合序列、图像和图三个分支
    """
    
    def __init__(self, config: Dict):
        """
        初始化多模态原型网络
        
        Args:
            config: 配置字典，包含所有模型参数
        """
        super(MultiModalProtoNet, self).__init__()
        
        self.config = config
        
        # 元学习参数
        self.n_way = config['protonet']['n_way']
        self.k_shot = config['protonet']['k_shot']
        self.temperature = config['protonet']['temperature']
        self.distance_metric = config['protonet']['distance_metric']
        
        # 对比学习参数
        self.cl_temperature = config['simclr']['temperature']
        self.cl_batch_size = config['simclr']['batch_size']
        self.cl_n_views = config['simclr']['n_views']
        
        # 设备
        self.device = config['training']['device']
        
        # 三个分支编码器
        self.sequence_encoder = TransformerEncoder(config['model']['sequence_branch'])
        self.image_encoder = ResNetEncoder(config['model']['image_branch'])
        self.graph_encoder = GCNEncoder(config['model']['graph_branch'])
        
        # 特征融合模块
        fusion_strategy = config['model']['fusion']['strategy']
        if fusion_strategy == 'attention':
            self.fusion_module = AttentionFusion(config['model']['fusion'])
        elif fusion_strategy == 'gate':
            self.fusion_module = GateFusion(config['model']['fusion'])
        elif fusion_strategy == 'concat':
            self.fusion_module = ConcatFusion(config['model']['fusion'])
        else:
            raise ValueError(f"Unknown fusion strategy: {fusion_strategy}")
        
        # 输出特征维度
        self.output_dim = config['model']['fusion']['output_dim']
        
        # 损失权重
        self.loss_weights = config.get('loss_weights', {
            'contrastive_loss': 1.0,
            'prototype_loss': 1.0,
            'fusion_regularization': 0.1
        })
        
        print(f"✅ MultiModalProtoNet initialized")
        print(f"✅ Fusion strategy: {fusion_strategy}")
        print(f"✅ Output dimension: {self.output_dim}")
        print(f"✅ N-way: {self.n_way}, K-shot: {self.k_shot}")
    
    def forward(self, multimodal_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        前向传播（用于对比学习）
        
        Args:
            multimodal_data: 多模态数据字典，包含：
                - sequence: 序列数据
                - image: 图像数据
                - graph: 图数据
                
        Returns:
            融合后的特征表示 [batch_size, output_dim]
        """
        # 三个分支特征提取
        sequence_features = self.sequence_encoder(multimodal_data['sequence'])
        image_features = self.image_encoder(multimodal_data['image'])
        graph_features = self.graph_encoder(multimodal_data['graph'])
        
        # 特征融合
        fused_features = self.fusion_module(sequence_features, image_features, graph_features)
        
        return fused_features
    
    def episode_forward(self, support_data: Dict[str, torch.Tensor], 
                       query_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Episode前向传播（用于元学习）
        
        Args:
            support_data: 支持集多模态数据
            query_data: 查询集多模态数据
            
        Returns:
            查询集的分类logits [n_query, n_way]
        """
        # 提取支持集特征
        support_features = self.extract_features(support_data)  # [n_way * k_shot, output_dim]
        
        # 提取查询集特征
        query_features = self.extract_features(query_data)      # [n_query, output_dim]
        
        # 计算原型
        prototypes = self.compute_prototypes(support_features)  # [n_way, output_dim]
        
        # 计算查询集到原型的距离/相似度
        logits = self.compute_logits(query_features, prototypes)  # [n_query, n_way]
        
        return logits
    
    def extract_features(self, multimodal_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        提取多模态特征
        
        Args:
            multimodal_data: 多模态数据
            
        Returns:
            融合后的特征 [batch_size, output_dim]
        """
        # 三个分支特征提取
        sequence_features = self.sequence_encoder(multimodal_data['sequence'])
        image_features = self.image_encoder(multimodal_data['image'])
        graph_features = self.graph_encoder(multimodal_data['graph'])
        
        # 特征融合
        fused_features = self.fusion_module(sequence_features, image_features, graph_features)
        
        # L2归一化（用于余弦相似度计算）
        fused_features = F.normalize(fused_features, dim=-1)

        return fused_features

    def compute_prototypes(self, support_features: torch.Tensor) -> torch.Tensor:
        """
        计算类别原型

        Args:
            support_features: 支持集特征 [n_way * k_shot, output_dim]

        Returns:
            类别原型 [n_way, output_dim]
        """
        # 重塑为 [n_way, k_shot, output_dim]
        support_features = support_features.view(self.n_way, self.k_shot, -1)

        # 计算每个类别的原型（均值）
        prototypes = support_features.mean(dim=1)  # [n_way, output_dim]

        # 归一化原型
        prototypes = F.normalize(prototypes, dim=-1)

        return prototypes

    def compute_logits(self, query_features: torch.Tensor, prototypes: torch.Tensor) -> torch.Tensor:
        """
        计算查询样本到原型的相似度logits

        Args:
            query_features: 查询集特征 [n_query, output_dim]
            prototypes: 类别原型 [n_way, output_dim]

        Returns:
            相似度logits [n_query, n_way]
        """
        if self.distance_metric == 'cosine':
            # 余弦相似度
            logits = torch.matmul(query_features, prototypes.t()) * self.temperature
        elif self.distance_metric == 'euclidean':
            # 欧几里得距离
            n_query = query_features.size(0)
            n_way = prototypes.size(0)

            # 扩展维度进行广播计算
            query_expanded = query_features.unsqueeze(1).expand(n_query, n_way, -1)
            proto_expanded = prototypes.unsqueeze(0).expand(n_query, n_way, -1)

            # 计算欧几里得距离
            distances = torch.pow(query_expanded - proto_expanded, 2).sum(dim=2)

            # 转换为相似度（距离越小，相似度越大）
            logits = -distances * self.temperature
        else:
            raise ValueError(f"Unknown distance metric: {self.distance_metric}")

        return logits

    def info_nce_loss(self, features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算InfoNCE对比学习损失

        Args:
            features: 特征张量 [batch_size * n_views, output_dim]

        Returns:
            (logits, labels) 用于交叉熵损失计算
        """
        device = features.device  # 使用特征张量的设备
        batch_size = features.size(0) // self.cl_n_views

        # 构建标签矩阵
        labels = torch.cat([torch.arange(batch_size) for _ in range(self.cl_n_views)], dim=0)
        labels = (labels.unsqueeze(0) == labels.unsqueeze(1)).float().to(device)

        # 特征归一化
        features = F.normalize(features, dim=1)

        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T)

        # 移除对角线元素
        mask = torch.eye(labels.shape[0], dtype=torch.bool).to(device)
        labels = labels[~mask].view(labels.shape[0], -1)
        similarity_matrix = similarity_matrix[~mask].view(similarity_matrix.shape[0], -1)

        # 分离正负样本
        positives = similarity_matrix[labels.bool()].view(labels.shape[0], -1)
        negatives = similarity_matrix[~labels.bool()].view(similarity_matrix.shape[0], -1)

        # 构建logits
        logits = torch.cat([positives, negatives], dim=1)
        labels = torch.zeros(logits.shape[0], dtype=torch.long).to(device)

        # 温度缩放
        logits = logits / self.cl_temperature

        return logits, labels

    def compute_fusion_regularization(self) -> torch.Tensor:
        """
        计算融合正则化损失（鼓励各模态均衡贡献）

        Returns:
            正则化损失
        """
        reg_loss = torch.tensor(0.0, device=self.device)

        # 根据融合策略计算不同的正则化
        if hasattr(self.fusion_module, 'get_attention_weights'):
            # 注意力融合的正则化
            weights = self.fusion_module.get_attention_weights()
            # 鼓励权重分布均匀
            uniform_dist = torch.ones_like(weights) / len(weights)
            reg_loss = F.kl_div(F.log_softmax(weights, dim=0), uniform_dist, reduction='sum')

        elif hasattr(self.fusion_module, 'get_modal_weights'):
            # 门控融合或拼接融合的正则化
            weights = self.fusion_module.get_modal_weights()
            uniform_dist = torch.ones_like(weights) / len(weights)
            reg_loss = F.kl_div(F.log_softmax(weights, dim=0), uniform_dist, reduction='sum')

        return reg_loss

    def get_individual_branch_features(self, multimodal_data: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        获取各个分支的独立特征（用于分析）

        Args:
            multimodal_data: 多模态数据

        Returns:
            各分支特征字典
        """
        with torch.no_grad():
            sequence_features = self.sequence_encoder(multimodal_data['sequence'])
            image_features = self.image_encoder(multimodal_data['image'])
            graph_features = self.graph_encoder(multimodal_data['graph'])

            return {
                'sequence': F.normalize(sequence_features, dim=-1),
                'image': F.normalize(image_features, dim=-1),
                'graph': F.normalize(graph_features, dim=-1)
            }

    def get_fusion_analysis(self, multimodal_data: Dict[str, torch.Tensor]) -> Dict:
        """
        获取融合分析信息（用于可视化和调试）

        Args:
            multimodal_data: 多模态数据

        Returns:
            融合分析信息字典
        """
        analysis = {}

        # 获取各分支特征
        branch_features = self.get_individual_branch_features(multimodal_data)
        analysis['branch_features'] = branch_features

        # 获取融合权重
        if hasattr(self.fusion_module, 'get_attention_weights'):
            analysis['fusion_weights'] = self.fusion_module.get_attention_weights()
        elif hasattr(self.fusion_module, 'get_modal_weights'):
            analysis['fusion_weights'] = self.fusion_module.get_modal_weights()

        # 计算分支间相似度
        seq_feat = branch_features['sequence']
        img_feat = branch_features['image']
        graph_feat = branch_features['graph']

        analysis['branch_similarities'] = {
            'seq_img': F.cosine_similarity(seq_feat, img_feat, dim=1).mean().item(),
            'seq_graph': F.cosine_similarity(seq_feat, graph_feat, dim=1).mean().item(),
            'img_graph': F.cosine_similarity(img_feat, graph_feat, dim=1).mean().item()
        }

        # 融合后特征
        fused_features = self.extract_features(multimodal_data)
        analysis['fused_features'] = fused_features

        return analysis

    def freeze_branch(self, branch_name: str):
        """
        冻结指定分支的参数

        Args:
            branch_name: 分支名称 ('sequence', 'image', 'graph')
        """
        if branch_name == 'sequence':
            for param in self.sequence_encoder.parameters():
                param.requires_grad = False
            print(f"✅ Sequence branch frozen")
        elif branch_name == 'image':
            for param in self.image_encoder.parameters():
                param.requires_grad = False
            print(f"✅ Image branch frozen")
        elif branch_name == 'graph':
            for param in self.graph_encoder.parameters():
                param.requires_grad = False
            print(f"✅ Graph branch frozen")
        else:
            raise ValueError(f"Unknown branch name: {branch_name}")

    def unfreeze_branch(self, branch_name: str):
        """
        解冻指定分支的参数

        Args:
            branch_name: 分支名称 ('sequence', 'image', 'graph')
        """
        if branch_name == 'sequence':
            for param in self.sequence_encoder.parameters():
                param.requires_grad = True
            print(f"✅ Sequence branch unfrozen")
        elif branch_name == 'image':
            for param in self.image_encoder.parameters():
                param.requires_grad = True
            print(f"✅ Image branch unfrozen")
        elif branch_name == 'graph':
            for param in self.graph_encoder.parameters():
                param.requires_grad = True
            print(f"✅ Graph branch unfrozen")
        else:
            raise ValueError(f"Unknown branch name: {branch_name}")

    def get_parameter_count(self) -> Dict[str, int]:
        """
        获取各部分参数数量统计

        Returns:
            参数数量字典
        """
        def count_parameters(module):
            return sum(p.numel() for p in module.parameters())

        def count_trainable_parameters(module):
            return sum(p.numel() for p in module.parameters() if p.requires_grad)

        return {
            'sequence_encoder_total': count_parameters(self.sequence_encoder),
            'sequence_encoder_trainable': count_trainable_parameters(self.sequence_encoder),
            'image_encoder_total': count_parameters(self.image_encoder),
            'image_encoder_trainable': count_trainable_parameters(self.image_encoder),
            'graph_encoder_total': count_parameters(self.graph_encoder),
            'graph_encoder_trainable': count_trainable_parameters(self.graph_encoder),
            'fusion_module_total': count_parameters(self.fusion_module),
            'fusion_module_trainable': count_trainable_parameters(self.fusion_module),
            'total_parameters': count_parameters(self),
            'total_trainable': count_trainable_parameters(self)
        }
