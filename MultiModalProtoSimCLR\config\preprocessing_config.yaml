# 自动化数据预处理配置文件

# 输入数据配置
input:
  raw_pcap_dir: "./raw_data"                    # 原始PCAP文件目录
  supported_extensions: [".pcap", ".pcapng"]    # 支持的文件扩展名

# 输出数据配置
output:
  processed_data_dir: "./processed_data"        # 处理后数据输出目录
  train_ratio: 0.7                             # 训练集比例
  val_ratio: 0.15                              # 验证集比例
  test_ratio: 0.15                             # 测试集比例

# 临时文件配置
temp:
  temp_dir: "./temp_processing"                 # 临时文件目录
  cleanup_temp: true                           # 是否清理临时文件

# 数据处理配置
processing:
  # 会话处理
  max_sessions_per_pcap: 1000                  # 每个PCAP文件最大会话数
  min_packets_per_session: 10                  # 每个会话最小包数
  max_file_size_mb: 100                        # 最大文件大小(MB)
  
  # 图像生成
  image_size: 32                               # RGB图像尺寸
  image_format: "png"                          # 图像格式
  
  # 序列数据
  sequence_length: 512                         # 序列最大长度
  vocab_size: 10000                           # 词汇表大小
  
  # 图数据
  graph_max_nodes: 100                        # 图最大节点数
  graph_feature_dim: 64                       # 图节点特征维度
  
  # 数据质量控制
  min_packet_size: 64                         # 最小包大小
  max_packet_size: 1500                       # 最大包大小
  filter_protocols: ["TCP", "UDP", "ICMP"]    # 保留的协议类型

# 并行处理配置
parallel:
  num_workers: 4                              # 并行处理数量
  batch_size: 10                              # 批处理大小
  use_multiprocessing: true                   # 是否使用多进程

# 数据验证配置
validation:
  check_corrupted_files: true                 # 是否检查损坏文件
  min_file_size_bytes: 1024                   # 最小文件大小(字节)
  max_processing_time_minutes: 30             # 最大处理时间(分钟)
  verify_output: true                         # 是否验证输出数据

# 会话分割配置
session_splitting:
  timeout_seconds: 300                        # 会话超时时间
  max_packets_per_session: 10000              # 每个会话最大包数
  split_by_protocol: true                     # 是否按协议分割
  extract_layers: ["al", "l7", "l5"]          # 提取的网络层

# 流量匿名化配置
anonymization:
  anonymize_ips: true                         # 是否匿名化IP地址
  anonymize_ports: false                      # 是否匿名化端口
  preserve_subnet: true                       # 是否保留子网结构
  hash_seed: 42                               # 哈希种子

# RGB图像生成配置
rgb_generation:
  pixel_per_byte: 1                           # 每字节像素数
  normalize_values: true                      # 是否归一化像素值
  padding_value: 0                            # 填充值
  color_mapping:                              # 颜色映射策略
    strategy: "protocol_based"                # protocol_based, payload_based, mixed
    tcp_color: [255, 0, 0]                   # TCP包颜色(红)
    udp_color: [0, 255, 0]                   # UDP包颜色(绿)
    icmp_color: [0, 0, 255]                  # ICMP包颜色(蓝)
    other_color: [128, 128, 128]             # 其他协议颜色(灰)

# 序列特征提取配置
sequence_extraction:
  tokenization_strategy: "protocol_aware"     # 分词策略
  include_timestamps: true                    # 是否包含时间戳
  include_packet_sizes: true                  # 是否包含包大小
  include_flow_features: true                 # 是否包含流特征
  special_tokens:
    pad_token: "<PAD>"
    unk_token: "<UNK>"
    cls_token: "<CLS>"
    sep_token: "<SEP>"

# 图特征提取配置
graph_extraction:
  node_features:                              # 节点特征
    - "degree"                               # 度数
    - "clustering_coefficient"               # 聚类系数
    - "betweenness_centrality"              # 介数中心性
    - "packet_count"                        # 数据包数量
    - "byte_count"                          # 字节数量
    - "avg_packet_size"                     # 平均包大小
  
  edge_features:                              # 边特征
    - "weight"                              # 连接权重
    - "protocol_type"                       # 协议类型
    - "direction"                           # 方向性
  
  graph_algorithms:                           # 图算法
    community_detection: "louvain"           # 社区检测算法
    centrality_measures: ["degree", "betweenness", "closeness"]
    
# 数据增强配置
augmentation:
  enable_augmentation: true                   # 是否启用数据增强
  
  sequence_augmentation:
    random_mask_prob: 0.15                   # 随机掩码概率
    random_replace_prob: 0.1                 # 随机替换概率
    noise_injection_prob: 0.05               # 噪声注入概率
  
  graph_augmentation:
    node_drop_prob: 0.1                     # 节点丢弃概率
    edge_drop_prob: 0.1                     # 边丢弃概率
    feature_noise_std: 0.1                  # 特征噪声标准差
  
  image_augmentation:
    rotation_angle: 5                        # 旋转角度
    brightness_factor: 0.1                  # 亮度因子
    contrast_factor: 0.1                    # 对比度因子

# 质量控制配置
quality_control:
  min_samples_per_class: 10                  # 每个类别最小样本数
  max_samples_per_class: 10000               # 每个类别最大样本数
  balance_classes: false                     # 是否平衡类别
  remove_outliers: true                      # 是否移除异常值
  outlier_detection_method: "isolation_forest" # 异常检测方法

# 日志配置
logging:
  level: "INFO"                              # 日志级别
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_to_file: true                         # 是否保存到文件
  log_file: "preprocessing.log"              # 日志文件名
  max_log_size_mb: 100                       # 最大日志文件大小

# 性能监控配置
monitoring:
  track_memory_usage: true                   # 是否跟踪内存使用
  track_processing_time: true                # 是否跟踪处理时间
  save_performance_stats: true              # 是否保存性能统计
  performance_log_interval: 100             # 性能日志间隔

# 错误处理配置
error_handling:
  max_retries: 3                            # 最大重试次数
  retry_delay_seconds: 5                    # 重试延迟时间
  skip_corrupted_files: true                # 是否跳过损坏文件
  continue_on_error: true                   # 是否在错误时继续
  error_log_file: "preprocessing_errors.log" # 错误日志文件
