#!/usr/bin/env python3
"""
端到端示例脚本 - 从原始PCAP到训练完成的完整流程
"""

import os
import sys
import time
import argparse
import subprocess
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.parent
sys.path.append(str(current_dir))


def run_command(cmd: str, description: str, check: bool = True) -> bool:
    """
    运行命令并显示进度
    
    Args:
        cmd: 要运行的命令
        description: 命令描述
        check: 是否检查返回码
        
    Returns:
        是否成功
    """
    print(f"\n🚀 {description}")
    print(f"命令: {cmd}")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, shell=True, check=check, capture_output=False)
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ {description} 完成 (耗时: {elapsed_time:.1f}秒)")
            return True
        else:
            print(f"❌ {description} 失败 (返回码: {result.returncode})")
            return False
            
    except subprocess.CalledProcessError as e:
        elapsed_time = time.time() - start_time
        print(f"❌ {description} 失败 (耗时: {elapsed_time:.1f}秒)")
        print(f"错误: {e}")
        return False
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断 {description}")
        return False


def check_dependencies():
    """
    检查依赖项
    """
    print("🔍 检查依赖项...")
    
    required_packages = [
        'torch', 'torchvision', 'scapy', 'networkx', 
        'scikit-learn', 'matplotlib', 'seaborn', 'tqdm', 'PyYAML'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {missing_packages}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖项都已安装")
    return True


def create_sample_data(output_dir: str, num_classes: int = 3, samples_per_class: int = 5):
    """
    创建示例PCAP数据（用于演示）
    
    Args:
        output_dir: 输出目录
        num_classes: 类别数量
        samples_per_class: 每个类别的样本数
    """
    print(f"\n📁 创建示例数据到 {output_dir}")
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 这里应该创建真实的PCAP文件，但为了演示，我们创建空文件
    for class_idx in range(num_classes):
        class_name = f"class_{class_idx + 1}"
        class_dir = output_path / class_name
        class_dir.mkdir(exist_ok=True)
        
        for sample_idx in range(samples_per_class):
            sample_file = class_dir / f"sample_{sample_idx + 1}.pcap"
            # 创建空的PCAP文件（实际使用中应该是真实的PCAP数据）
            sample_file.touch()
    
    print(f"✅ 创建了 {num_classes} 个类别，每个类别 {samples_per_class} 个样本")


def main():
    """
    主函数 - 端到端流程
    """
    parser = argparse.ArgumentParser(
        description='端到端多模态网络入侵检测系统演示',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
完整流程包括:
1. 检查依赖项
2. 数据预处理 (PCAP -> 多模态数据)
3. 数据验证
4. 模型训练 (对比学习 + 元学习)
5. 模型评估

示例用法:
  # 使用现有PCAP数据
  python scripts/end_to_end_example.py --input ./raw_pcap_data --output ./experiment_results
  
  # 创建示例数据并运行完整流程
  python scripts/end_to_end_example.py --create-sample-data --output ./experiment_results
        """
    )
    
    # 数据相关参数
    parser.add_argument('--input', type=str, help='原始PCAP数据目录')
    parser.add_argument('--output', type=str, required=True, help='实验输出目录')
    parser.add_argument('--create-sample-data', action='store_true', help='创建示例数据用于演示')
    
    # 预处理参数
    parser.add_argument('--skip-preprocessing', action='store_true', help='跳过数据预处理')
    parser.add_argument('--workers', type=int, default=4, help='预处理并行数量')
    parser.add_argument('--image-size', type=int, default=32, help='RGB图像尺寸')
    
    # 训练参数
    parser.add_argument('--skip-training', action='store_true', help='跳过模型训练')
    parser.add_argument('--stage1-epochs', type=int, default=10, help='对比学习轮数')
    parser.add_argument('--stage2-epochs', type=int, default=20, help='元学习轮数')
    parser.add_argument('--gpu', type=int, default=0, help='GPU设备ID')
    
    # 其他参数
    parser.add_argument('--skip-validation', action='store_true', help='跳过数据验证')
    parser.add_argument('--skip-evaluation', action='store_true', help='跳过模型评估')
    parser.add_argument('--keep-temp', action='store_true', help='保留临时文件')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("🚀 多模态网络入侵检测系统 - 端到端演示")
    print("=" * 60)
    print(f"输出目录: {output_dir}")
    print("=" * 60)
    
    # 1. 检查依赖项
    if not check_dependencies():
        print("❌ 依赖项检查失败，请先安装必需的包")
        return 1
    
    # 2. 准备数据
    if args.create_sample_data:
        raw_data_dir = output_dir / "raw_data"
        create_sample_data(str(raw_data_dir))
        input_dir = raw_data_dir
    elif args.input:
        input_dir = Path(args.input)
        if not input_dir.exists():
            print(f"❌ 输入目录不存在: {input_dir}")
            return 1
    else:
        print("❌ 请指定输入数据目录或使用 --create-sample-data 创建示例数据")
        return 1
    
    # 3. 数据预处理
    processed_data_dir = output_dir / "processed_data"
    
    if not args.skip_preprocessing:
        preprocess_cmd = f"""python scripts/preprocess_data.py \
            --input "{input_dir}" \
            --output "{processed_data_dir}" \
            --workers {args.workers} \
            --image-size {args.image_size}"""
        
        if args.keep_temp:
            preprocess_cmd += " --no-cleanup"
        
        success = run_command(preprocess_cmd, "数据预处理")
        if not success:
            print("❌ 数据预处理失败")
            return 1
    else:
        print("⏭️ 跳过数据预处理")
    
    # 4. 数据验证
    if not args.skip_validation and processed_data_dir.exists():
        validation_dir = output_dir / "validation"
        validate_cmd = f"""python scripts/validate_processed_data.py \
            --data-dir "{processed_data_dir}" \
            --output-dir "{validation_dir}" \
            --report-file "{validation_dir}/validation_report.md" """
        
        success = run_command(validate_cmd, "数据验证", check=False)
        if not success:
            print("⚠️ 数据验证失败，但继续执行")
    else:
        print("⏭️ 跳过数据验证")
    
    # 5. 模型训练
    if not args.skip_training:
        # 更新配置文件中的数据路径
        config_file = current_dir / "config" / "multimodal_config.yaml"
        
        if config_file.exists():
            # 读取并更新配置
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新数据路径
            config['data']['train_dataset'] = str(processed_data_dir / "train")
            config['data']['val_dataset'] = str(processed_data_dir / "val")
            config['data']['test_dataset'] = str(processed_data_dir / "test")
            
            # 更新训练参数
            config['training']['stage1_epochs'] = args.stage1_epochs
            config['training']['stage2_epochs'] = args.stage2_epochs
            config['training']['device'] = f'cuda:{args.gpu}' if args.gpu >= 0 else 'cpu'
            
            # 更新日志路径
            config['logging']['save_dir'] = str(output_dir / "checkpoints")
            config['logging']['log_dir'] = str(output_dir / "logs")
            
            # 保存更新后的配置
            temp_config_file = output_dir / "training_config.yaml"
            with open(temp_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False)
            
            # 训练命令
            train_cmd = f"""python experiments/train_multimodal.py \
                --config "{temp_config_file}" \
                --gpu {args.gpu}"""
            
            success = run_command(train_cmd, "模型训练")
            if not success:
                print("❌ 模型训练失败")
                return 1
        else:
            print(f"❌ 配置文件不存在: {config_file}")
            return 1
    else:
        print("⏭️ 跳过模型训练")
    
    # 6. 模型评估
    if not args.skip_evaluation:
        checkpoint_dir = output_dir / "checkpoints"
        best_checkpoint = checkpoint_dir / "stage2_best.pth"
        
        if best_checkpoint.exists():
            evaluation_dir = output_dir / "evaluation"
            eval_cmd = f"""python experiments/evaluate_multimodal.py \
                --config "{temp_config_file}" \
                --checkpoint "{best_checkpoint}" \
                --test-data "{processed_data_dir}/test" \
                --episodes 50 \
                --gpu {args.gpu} \
                --output-dir "{evaluation_dir}" """
            
            success = run_command(eval_cmd, "模型评估", check=False)
            if not success:
                print("⚠️ 模型评估失败，但流程已完成")
        else:
            print("⚠️ 未找到训练好的模型，跳过评估")
    else:
        print("⏭️ 跳过模型评估")
    
    # 7. 生成总结报告
    print("\n📋 生成总结报告...")
    
    summary_file = output_dir / "experiment_summary.md"
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write("# 多模态网络入侵检测实验总结\n\n")
        f.write(f"实验时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"## 实验配置\n")
        f.write(f"- 输入数据: {input_dir}\n")
        f.write(f"- 输出目录: {output_dir}\n")
        f.write(f"- 图像尺寸: {args.image_size}x{args.image_size}\n")
        f.write(f"- 对比学习轮数: {args.stage1_epochs}\n")
        f.write(f"- 元学习轮数: {args.stage2_epochs}\n\n")
        
        f.write(f"## 生成的文件\n")
        f.write(f"- 预处理数据: `{processed_data_dir}`\n")
        f.write(f"- 模型检查点: `{output_dir}/checkpoints/`\n")
        f.write(f"- 训练日志: `{output_dir}/logs/`\n")
        f.write(f"- 评估结果: `{output_dir}/evaluation/`\n")
        f.write(f"- 数据验证: `{output_dir}/validation/`\n\n")
        
        f.write(f"## 下一步\n")
        f.write(f"1. 查看训练日志: `tensorboard --logdir {output_dir}/logs`\n")
        f.write(f"2. 检查评估结果: `cat {output_dir}/evaluation/evaluation_results.yaml`\n")
        f.write(f"3. 使用训练好的模型进行推理\n")
    
    print(f"✅ 总结报告已保存到: {summary_file}")
    
    print("\n🎉 端到端流程完成!")
    print(f"📁 所有结果已保存到: {output_dir}")
    print("\n📋 查看结果:")
    print(f"  - 实验总结: cat {summary_file}")
    print(f"  - 数据验证: cat {output_dir}/validation/validation_report.md")
    print(f"  - 评估结果: cat {output_dir}/evaluation/evaluation_results.yaml")
    
    return 0


if __name__ == "__main__":
    exit(main())
