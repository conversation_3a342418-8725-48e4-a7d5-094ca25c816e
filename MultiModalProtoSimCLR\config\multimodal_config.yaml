# 多模态三分支ProtoSimCLR配置文件

# 数据相关配置
data:
  # 数据路径
  pcap_data_root: "../ProcessPCAP/final/ISAC218_processed"
  train_dataset: "../ProcessPCAP/final/ISAC218_processed/train"
  val_dataset: "../ProcessPCAP/final/ISAC218_processed/val"
  test_dataset: "../ProcessPCAP/final/ISAC218_processed/test"
  
  # 数据预处理参数
  sequence_length: 512        # 序列最大长度
  graph_max_nodes: 100       # 图最大节点数
  img_size: 32               # 图像尺寸
  img_channels: 3            # 图像通道数
  
  # 数据集类型
  dataset_type: "MultiModalFewShotDataset"
  num_classes: 10

# 模型架构配置
model:
  # 序列分支配置 (Transformer)
  sequence_branch:
    vocab_size: 10000          # 词汇表大小
    embedding_dim: 256         # 嵌入维度
    num_heads: 8               # 注意力头数
    num_layers: 6              # Transformer层数
    hidden_dim: 1024           # 前馈网络隐藏维度
    dropout: 0.1               # Dropout率
    max_position_embeddings: 512
    output_dim: 256            # 输出特征维度
  
  # 图像分支配置 (ResNet + SimCLR)
  image_branch:
    base_model: "resnet18"     # 基础模型
    pretrained: true           # 是否使用预训练权重
    freeze_backbone: false     # 是否冻结骨干网络
    simclr_dim: 128           # SimCLR投影维度
    output_dim: 256           # 输出特征维度
  
  # 图分支配置 (GCN)
  graph_branch:
    input_dim: 64             # 节点特征维度
    hidden_dims: [128, 256]   # 隐藏层维度列表
    num_layers: 3             # GCN层数
    dropout: 0.2              # Dropout率
    activation: "relu"        # 激活函数
    output_dim: 256           # 输出特征维度
    pooling: "global_mean"    # 图池化方式
  
  # 特征融合配置
  fusion:
    strategy: "attention"      # 融合策略: attention, gate, concat
    fusion_dim: 768           # 融合后特征维度 (3 * 256)
    attention_heads: 8        # 注意力头数
    dropout: 0.1              # Dropout率
    output_dim: 512           # 最终输出维度

# SimCLR对比学习配置
simclr:
  temperature: 0.07           # 温度参数
  batch_size: 64             # 批次大小
  n_views: 2                 # 视图数量
  projection_dim: 128        # 投影头维度

# ProtoNet元学习配置
protonet:
  n_way: 5                   # N-way分类
  k_shot: 3                  # K-shot学习
  k_query: 12                # 查询集样本数
  episodes_num: 200          # 每个epoch的episode数
  temperature: 10.0          # 原型匹配温度参数
  distance_metric: "cosine"  # 距离度量方式

# 训练配置
training:
  # 两阶段训练
  stage1_epochs: 100         # 对比学习预训练轮数
  stage2_epochs: 200         # 元学习微调轮数
  
  # 优化器配置
  optimizer: "adam"
  learning_rate: 0.001
  weight_decay: 1e-4
  momentum: 0.9              # SGD动量
  
  # 学习率调度
  lr_scheduler:
    type: "cosine"
    T_max: 300
    eta_min: 1e-6
  
  # 训练参数
  batch_size: 32
  num_workers: 4
  device: "cuda"
  seed: 42
  gradient_clip_norm: 1.0
  early_stopping_patience: 20

# 损失函数权重
loss_weights:
  contrastive_loss: 1.0      # 对比学习损失权重
  prototype_loss: 1.0        # 原型网络损失权重
  fusion_regularization: 0.1  # 融合正则化权重

# 评估配置
evaluation:
  eval_freq: 5               # 评估频率
  test_episodes: 100         # 测试episode数
  metrics: ["accuracy", "precision", "recall", "f1_score"]
  save_predictions: true     # 是否保存预测结果

# 日志和保存配置
logging:
  save_dir: "./checkpoints/multimodal"
  log_dir: "./logs/multimodal"
  tensorboard_dir: "./runs/multimodal"
  save_freq: 10              # 模型保存频率
  log_freq: 100              # 日志记录频率
  experiment_name: "multimodal_protonet_v1"

# 数据增强配置
augmentation:
  # 序列数据增强
  sequence:
    random_mask_prob: 0.15   # 随机掩码概率
    random_replace_prob: 0.1  # 随机替换概率
    
  # 图像数据增强
  image:
    horizontal_flip: true
    vertical_flip: false
    rotation: 10
    color_jitter:
      brightness: 0.2
      contrast: 0.2
      saturation: 0.2
      hue: 0.1
    gaussian_blur:
      kernel_size: 3
      sigma: [0.1, 2.0]
    normalize:
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
  
  # 图数据增强
  graph:
    node_drop_prob: 0.1      # 节点丢弃概率
    edge_drop_prob: 0.1      # 边丢弃概率
    feature_noise_std: 0.1   # 特征噪声标准差

# 实验配置
experiment:
  ablation_study: false      # 是否进行消融实验
  fusion_strategies: ["attention", "gate", "concat"]  # 要测试的融合策略
  modality_combinations: [   # 要测试的模态组合
    ["sequence", "image", "graph"],
    ["sequence", "image"],
    ["sequence", "graph"],
    ["image", "graph"]
  ]
