"""
Transformer编码器 - 用于处理序列数据
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Dict, Optional, Tuple


class PositionalEncoding(nn.Module):
    """
    位置编码模块
    """
    
    def __init__(self, d_model: int, max_len: int = 5000):
        """
        初始化位置编码
        
        Args:
            d_model: 模型维度
            max_len: 最大序列长度
        """
        super(PositionalEncoding, self).__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [seq_len, batch_size, d_model]
            
        Returns:
            添加位置编码后的张量
        """
        return x + self.pe[:x.size(0), :]


class TransformerEncoder(nn.Module):
    """
    Transformer编码器，用于处理网络流量序列数据
    """
    
    def __init__(self, config: Dict):
        """
        初始化Transformer编码器
        
        Args:
            config: 配置字典，包含模型参数
        """
        super(TransformerEncoder, self).__init__()
        
        # 从配置中提取参数
        self.vocab_size = config.get('vocab_size', 10000)
        self.embedding_dim = config.get('embedding_dim', 256)
        self.num_heads = config.get('num_heads', 8)
        self.num_layers = config.get('num_layers', 6)
        self.hidden_dim = config.get('hidden_dim', 1024)
        self.dropout = config.get('dropout', 0.1)
        self.max_position_embeddings = config.get('max_position_embeddings', 512)
        self.output_dim = config.get('output_dim', 256)
        
        # Token嵌入层
        self.token_embedding = nn.Embedding(self.vocab_size, self.embedding_dim)
        
        # 位置编码
        self.position_encoding = PositionalEncoding(self.embedding_dim, self.max_position_embeddings)
        
        # 时间特征投影层
        self.time_projection = nn.Linear(3, self.embedding_dim)  # 3个时间特征
        
        # 原始特征投影层
        self.feature_projection = nn.Linear(8, self.embedding_dim)  # 8个原始特征
        
        # Transformer编码器层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=self.embedding_dim,
            nhead=self.num_heads,
            dim_feedforward=self.hidden_dim,
            dropout=self.dropout,
            activation='relu',
            batch_first=True
        )
        
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer,
            num_layers=self.num_layers
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(self.embedding_dim)
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(self.embedding_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dim, self.output_dim)
        )
        
        # 全局池化策略
        self.pooling_strategy = config.get('pooling_strategy', 'cls')  # 'cls', 'mean', 'max'
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化模型权重
        """
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, mean=0, std=0.02)
            elif isinstance(module, nn.LayerNorm):
                nn.init.constant_(module.bias, 0)
                nn.init.constant_(module.weight, 1.0)
    
    def forward(self, sequence_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        前向传播
        
        Args:
            sequence_data: 序列数据字典，包含：
                - input_ids: token序列 [batch_size, seq_len]
                - attention_mask: 注意力掩码 [batch_size, seq_len]
                - time_features: 时间特征 [batch_size, seq_len, 3]
                - raw_features: 原始特征 [batch_size, seq_len, 8]
                
        Returns:
            编码后的特征向量 [batch_size, output_dim]
        """
        input_ids = sequence_data['input_ids']
        attention_mask = sequence_data['attention_mask']
        time_features = sequence_data.get('time_features')
        raw_features = sequence_data.get('raw_features')
        
        batch_size, seq_len = input_ids.shape
        
        # Token嵌入
        token_embeddings = self.token_embedding(input_ids)  # [batch_size, seq_len, embedding_dim]
        
        # 组合多种特征
        embeddings = token_embeddings
        
        # 添加时间特征
        if time_features is not None:
            time_embeddings = self.time_projection(time_features)
            embeddings = embeddings + time_embeddings
        
        # 添加原始特征
        if raw_features is not None:
            feature_embeddings = self.feature_projection(raw_features)
            embeddings = embeddings + feature_embeddings
        
        # 添加位置编码
        embeddings = embeddings.transpose(0, 1)  # [seq_len, batch_size, embedding_dim]
        embeddings = self.position_encoding(embeddings)
        embeddings = embeddings.transpose(0, 1)  # [batch_size, seq_len, embedding_dim]
        
        # 层归一化
        embeddings = self.layer_norm(embeddings)
        
        # 创建注意力掩码（True表示需要忽略的位置）
        attention_mask = attention_mask == 0
        
        # Transformer编码
        encoded = self.transformer_encoder(
            embeddings,
            src_key_padding_mask=attention_mask
        )  # [batch_size, seq_len, embedding_dim]
        
        # 全局池化
        pooled_output = self._global_pooling(encoded, attention_mask)
        
        # 输出投影
        output = self.output_projection(pooled_output)
        
        return output
    
    def _global_pooling(self, encoded: torch.Tensor, attention_mask: torch.Tensor) -> torch.Tensor:
        """
        全局池化操作
        
        Args:
            encoded: 编码后的序列 [batch_size, seq_len, embedding_dim]
            attention_mask: 注意力掩码 [batch_size, seq_len]
            
        Returns:
            池化后的特征 [batch_size, embedding_dim]
        """
        if self.pooling_strategy == 'cls':
            # 使用CLS token（第一个token）
            return encoded[:, 0, :]
        
        elif self.pooling_strategy == 'mean':
            # 平均池化（忽略padding）
            mask = (~attention_mask).float().unsqueeze(-1)  # [batch_size, seq_len, 1]
            masked_encoded = encoded * mask
            sum_encoded = masked_encoded.sum(dim=1)  # [batch_size, embedding_dim]
            seq_lengths = mask.sum(dim=1)  # [batch_size, 1]
            return sum_encoded / (seq_lengths + 1e-8)
        
        elif self.pooling_strategy == 'max':
            # 最大池化
            mask = attention_mask.unsqueeze(-1).expand_as(encoded)
            masked_encoded = encoded.masked_fill(mask, float('-inf'))
            return masked_encoded.max(dim=1)[0]
        
        else:
            raise ValueError(f"Unknown pooling strategy: {self.pooling_strategy}")
    
    def get_attention_weights(self, sequence_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        获取注意力权重（用于可视化）
        
        Args:
            sequence_data: 序列数据字典
            
        Returns:
            注意力权重 [batch_size, num_heads, seq_len, seq_len]
        """
        # 这里需要修改transformer_encoder以返回注意力权重
        # 为简化实现，暂时返回None
        return None
    
    def encode_with_layers(self, sequence_data: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        逐层编码，返回中间层特征（用于分析）
        
        Args:
            sequence_data: 序列数据字典
            
        Returns:
            包含各层特征的字典
        """
        input_ids = sequence_data['input_ids']
        attention_mask = sequence_data['attention_mask']
        time_features = sequence_data.get('time_features')
        raw_features = sequence_data.get('raw_features')
        
        # Token嵌入
        token_embeddings = self.token_embedding(input_ids)
        embeddings = token_embeddings
        
        # 添加其他特征
        if time_features is not None:
            time_embeddings = self.time_projection(time_features)
            embeddings = embeddings + time_embeddings
        
        if raw_features is not None:
            feature_embeddings = self.feature_projection(raw_features)
            embeddings = embeddings + feature_embeddings
        
        # 位置编码
        embeddings = embeddings.transpose(0, 1)
        embeddings = self.position_encoding(embeddings)
        embeddings = embeddings.transpose(0, 1)
        
        # 层归一化
        embeddings = self.layer_norm(embeddings)
        
        # 逐层编码
        layer_outputs = {'input_embeddings': embeddings}
        
        attention_mask = attention_mask == 0
        current_output = embeddings
        
        for i, layer in enumerate(self.transformer_encoder.layers):
            current_output = layer(current_output, src_key_padding_mask=attention_mask)
            layer_outputs[f'layer_{i}'] = current_output
        
        # 全局池化和输出投影
        pooled_output = self._global_pooling(current_output, attention_mask)
        final_output = self.output_projection(pooled_output)
        
        layer_outputs['pooled_output'] = pooled_output
        layer_outputs['final_output'] = final_output
        
        return layer_outputs


if __name__ == "__main__":
    # 测试代码
    config = {
        'vocab_size': 1000,
        'embedding_dim': 256,
        'num_heads': 8,
        'num_layers': 6,
        'hidden_dim': 1024,
        'dropout': 0.1,
        'max_position_embeddings': 512,
        'output_dim': 256,
        'pooling_strategy': 'cls'
    }
    
    encoder = TransformerEncoder(config)
    
    # 测试数据
    batch_size, seq_len = 4, 100
    sequence_data = {
        'input_ids': torch.randint(0, 1000, (batch_size, seq_len)),
        'attention_mask': torch.ones(batch_size, seq_len),
        'time_features': torch.randn(batch_size, seq_len, 3),
        'raw_features': torch.randn(batch_size, seq_len, 8)
    }
    
    # 前向传播
    output = encoder(sequence_data)
    print(f"Output shape: {output.shape}")
    
    # 逐层编码测试
    layer_outputs = encoder.encode_with_layers(sequence_data)
    print(f"Layer outputs keys: {layer_outputs.keys()}")
    print(f"Final output shape: {layer_outputs['final_output'].shape}")
