"""
图卷积网络编码器 - 用于处理图数据
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional
import math


class GraphConvLayer(nn.Module):
    """
    图卷积层
    """
    
    def __init__(self, input_dim: int, output_dim: int, activation: str = 'relu', dropout: float = 0.0):
        """
        初始化图卷积层
        
        Args:
            input_dim: 输入特征维度
            output_dim: 输出特征维度
            activation: 激活函数类型
            dropout: Dropout率
        """
        super(GraphConvLayer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        # 线性变换层
        self.linear = nn.Linear(input_dim, output_dim)
        
        # 激活函数
        if activation == 'relu':
            self.activation = nn.ReLU()
        elif activation == 'leaky_relu':
            self.activation = nn.LeakyReLU()
        elif activation == 'tanh':
            self.activation = nn.Tanh()
        elif activation == 'none':
            self.activation = nn.Identity()
        else:
            raise ValueError(f"Unknown activation: {activation}")
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 批归一化
        self.batch_norm = nn.BatchNorm1d(output_dim)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化权重
        """
        nn.init.xavier_uniform_(self.linear.weight)
        if self.linear.bias is not None:
            nn.init.constant_(self.linear.bias, 0)
    
    def forward(self, x: torch.Tensor, adj: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 节点特征 [batch_size, num_nodes, input_dim]
            adj: 邻接矩阵 [batch_size, num_nodes, num_nodes]
            
        Returns:
            输出特征 [batch_size, num_nodes, output_dim]
        """
        batch_size, num_nodes, _ = x.shape
        
        # 线性变换
        h = self.linear(x)  # [batch_size, num_nodes, output_dim]
        
        # 图卷积：AH
        # 添加自环
        adj_with_self_loop = adj + torch.eye(num_nodes, device=adj.device).unsqueeze(0)
        
        # 度矩阵归一化
        degree = adj_with_self_loop.sum(dim=-1, keepdim=True)  # [batch_size, num_nodes, 1]
        degree_inv_sqrt = torch.pow(degree + 1e-8, -0.5)
        
        # 对称归一化：D^(-1/2) * A * D^(-1/2)
        norm_adj = degree_inv_sqrt * adj_with_self_loop * degree_inv_sqrt.transpose(-2, -1)
        
        # 图卷积操作
        output = torch.bmm(norm_adj, h)  # [batch_size, num_nodes, output_dim]
        
        # 批归一化（需要重塑为2D）
        output_2d = output.view(-1, self.output_dim)
        output_2d = self.batch_norm(output_2d)
        output = output_2d.view(batch_size, num_nodes, self.output_dim)
        
        # 激活函数
        output = self.activation(output)
        
        # Dropout
        output = self.dropout(output)
        
        return output


class GraphAttentionLayer(nn.Module):
    """
    图注意力层
    """
    
    def __init__(self, input_dim: int, output_dim: int, num_heads: int = 1, dropout: float = 0.0):
        """
        初始化图注意力层
        
        Args:
            input_dim: 输入特征维度
            output_dim: 输出特征维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super(GraphAttentionLayer, self).__init__()
        
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.num_heads = num_heads
        self.head_dim = output_dim // num_heads
        
        assert output_dim % num_heads == 0, "output_dim must be divisible by num_heads"
        
        # 线性变换层
        self.W = nn.Linear(input_dim, output_dim)
        self.a = nn.Linear(2 * self.head_dim, 1)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 激活函数
        self.leaky_relu = nn.LeakyReLU(0.2)
        
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化权重
        """
        nn.init.xavier_uniform_(self.W.weight)
        nn.init.xavier_uniform_(self.a.weight)
    
    def forward(self, x: torch.Tensor, adj: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 节点特征 [batch_size, num_nodes, input_dim]
            adj: 邻接矩阵 [batch_size, num_nodes, num_nodes]
            
        Returns:
            输出特征 [batch_size, num_nodes, output_dim]
        """
        batch_size, num_nodes, _ = x.shape
        
        # 线性变换
        h = self.W(x)  # [batch_size, num_nodes, output_dim]
        
        # 重塑为多头
        h = h.view(batch_size, num_nodes, self.num_heads, self.head_dim)
        
        # 计算注意力权重
        attention_scores = self._compute_attention(h, adj)  # [batch_size, num_heads, num_nodes, num_nodes]
        
        # 应用注意力权重
        h = h.transpose(1, 2)  # [batch_size, num_heads, num_nodes, head_dim]
        output = torch.matmul(attention_scores, h)  # [batch_size, num_heads, num_nodes, head_dim]
        
        # 合并多头
        output = output.transpose(1, 2).contiguous()  # [batch_size, num_nodes, num_heads, head_dim]
        output = output.view(batch_size, num_nodes, self.output_dim)
        
        return output
    
    def _compute_attention(self, h: torch.Tensor, adj: torch.Tensor) -> torch.Tensor:
        """
        计算注意力权重
        
        Args:
            h: 节点特征 [batch_size, num_nodes, num_heads, head_dim]
            adj: 邻接矩阵 [batch_size, num_nodes, num_nodes]
            
        Returns:
            注意力权重 [batch_size, num_heads, num_nodes, num_nodes]
        """
        batch_size, num_nodes, num_heads, head_dim = h.shape
        
        # 扩展邻接矩阵
        adj = adj.unsqueeze(1).expand(batch_size, num_heads, num_nodes, num_nodes)
        
        attention_scores = torch.zeros_like(adj, dtype=torch.float)
        
        for head in range(num_heads):
            h_head = h[:, :, head, :]  # [batch_size, num_nodes, head_dim]
            
            # 计算所有节点对的注意力分数
            h_i = h_head.unsqueeze(2).expand(batch_size, num_nodes, num_nodes, head_dim)
            h_j = h_head.unsqueeze(1).expand(batch_size, num_nodes, num_nodes, head_dim)
            
            # 拼接特征
            concat_features = torch.cat([h_i, h_j], dim=-1)  # [batch_size, num_nodes, num_nodes, 2*head_dim]
            
            # 计算注意力分数
            e = self.a(concat_features).squeeze(-1)  # [batch_size, num_nodes, num_nodes]
            e = self.leaky_relu(e)
            
            # 掩码非邻接节点
            e = e.masked_fill(adj[:, head] == 0, float('-inf'))
            
            # Softmax归一化
            attention_scores[:, head] = F.softmax(e, dim=-1)
        
        # 应用dropout
        attention_scores = self.dropout(attention_scores)
        
        return attention_scores


class GCNEncoder(nn.Module):
    """
    图卷积网络编码器
    """
    
    def __init__(self, config: Dict):
        """
        初始化GCN编码器
        
        Args:
            config: 配置字典
        """
        super(GCNEncoder, self).__init__()
        
        # 从配置中提取参数
        self.input_dim = config.get('input_dim', 64)
        self.hidden_dims = config.get('hidden_dims', [128, 256])
        self.num_layers = config.get('num_layers', 3)
        self.dropout = config.get('dropout', 0.2)
        self.activation = config.get('activation', 'relu')
        self.output_dim = config.get('output_dim', 256)
        self.pooling = config.get('pooling', 'global_mean')
        self.use_attention = config.get('use_attention', False)
        self.attention_heads = config.get('attention_heads', 4)
        
        # 构建网络层
        self.layers = nn.ModuleList()
        
        # 输入层
        current_dim = self.input_dim
        
        # 隐藏层
        for i, hidden_dim in enumerate(self.hidden_dims):
            if self.use_attention and i > 0:  # 在中间层使用注意力
                layer = GraphAttentionLayer(
                    input_dim=current_dim,
                    output_dim=hidden_dim,
                    num_heads=self.attention_heads,
                    dropout=self.dropout
                )
            else:
                layer = GraphConvLayer(
                    input_dim=current_dim,
                    output_dim=hidden_dim,
                    activation=self.activation,
                    dropout=self.dropout
                )
            
            self.layers.append(layer)
            current_dim = hidden_dim
        
        # 输出层
        if current_dim != self.output_dim:
            output_layer = GraphConvLayer(
                input_dim=current_dim,
                output_dim=self.output_dim,
                activation='none',  # 输出层不使用激活函数
                dropout=0.0
            )
            self.layers.append(output_layer)
        
        # 全局特征融合层
        self.global_fusion = nn.Sequential(
            nn.Linear(15, 64),  # 15是全局特征维度
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(64, self.output_dim // 4)
        )
        
        # 最终输出层
        self.final_projection = nn.Sequential(
            nn.Linear(self.output_dim + self.output_dim // 4, self.output_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.output_dim, self.output_dim)
        )
    
    def forward(self, graph_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        前向传播
        
        Args:
            graph_data: 图数据字典，包含：
                - x: 节点特征 [batch_size, num_nodes, input_dim]
                - adj_matrix: 邻接矩阵 [batch_size, num_nodes, num_nodes]
                - global_features: 全局特征 [batch_size, global_dim]
                
        Returns:
            编码后的图特征 [batch_size, output_dim]
        """
        x = graph_data['x']
        adj_matrix = graph_data['adj_matrix']
        global_features = graph_data['global_features']
        
        # 逐层前向传播
        h = x
        for layer in self.layers:
            h = layer(h, adj_matrix)
        
        # 图级别池化
        graph_embedding = self._graph_pooling(h, adj_matrix)
        
        # 全局特征处理
        global_embedding = self.global_fusion(global_features)
        
        # 特征融合
        combined_features = torch.cat([graph_embedding, global_embedding], dim=-1)
        
        # 最终投影
        output = self.final_projection(combined_features)
        
        return output
    
    def _graph_pooling(self, node_features: torch.Tensor, adj_matrix: torch.Tensor) -> torch.Tensor:
        """
        图级别池化操作
        
        Args:
            node_features: 节点特征 [batch_size, num_nodes, feature_dim]
            adj_matrix: 邻接矩阵 [batch_size, num_nodes, num_nodes]
            
        Returns:
            图级别特征 [batch_size, feature_dim]
        """
        if self.pooling == 'global_mean':
            # 全局平均池化
            return torch.mean(node_features, dim=1)
        
        elif self.pooling == 'global_max':
            # 全局最大池化
            return torch.max(node_features, dim=1)[0]
        
        elif self.pooling == 'global_sum':
            # 全局求和池化
            return torch.sum(node_features, dim=1)
        
        elif self.pooling == 'attention':
            # 注意力池化
            return self._attention_pooling(node_features, adj_matrix)
        
        else:
            raise ValueError(f"Unknown pooling method: {self.pooling}")
    
    def _attention_pooling(self, node_features: torch.Tensor, adj_matrix: torch.Tensor) -> torch.Tensor:
        """
        注意力池化
        
        Args:
            node_features: 节点特征 [batch_size, num_nodes, feature_dim]
            adj_matrix: 邻接矩阵 [batch_size, num_nodes, num_nodes]
            
        Returns:
            池化后的特征 [batch_size, feature_dim]
        """
        batch_size, num_nodes, feature_dim = node_features.shape
        
        # 计算注意力权重
        attention_weights = torch.sum(node_features, dim=-1)  # [batch_size, num_nodes]
        attention_weights = F.softmax(attention_weights, dim=-1)  # [batch_size, num_nodes]
        
        # 加权求和
        weighted_features = node_features * attention_weights.unsqueeze(-1)
        pooled_features = torch.sum(weighted_features, dim=1)
        
        return pooled_features
    
    def get_node_embeddings(self, graph_data: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        获取节点嵌入（用于可视化和分析）
        
        Args:
            graph_data: 图数据字典
            
        Returns:
            节点嵌入 [batch_size, num_nodes, output_dim]
        """
        x = graph_data['x']
        adj_matrix = graph_data['adj_matrix']
        
        # 逐层前向传播
        h = x
        for layer in self.layers:
            h = layer(h, adj_matrix)
        
        return h


if __name__ == "__main__":
    # 测试代码
    config = {
        'input_dim': 6,
        'hidden_dims': [64, 128],
        'num_layers': 3,
        'dropout': 0.2,
        'activation': 'relu',
        'output_dim': 256,
        'pooling': 'global_mean',
        'use_attention': True,
        'attention_heads': 4
    }
    
    encoder = GCNEncoder(config)
    
    # 测试数据
    batch_size, num_nodes = 4, 50
    graph_data = {
        'x': torch.randn(batch_size, num_nodes, 6),
        'adj_matrix': torch.randint(0, 2, (batch_size, num_nodes, num_nodes)).float(),
        'global_features': torch.randn(batch_size, 15)
    }
    
    # 前向传播
    output = encoder(graph_data)
    print(f"Output shape: {output.shape}")
    
    # 获取节点嵌入
    node_embeddings = encoder.get_node_embeddings(graph_data)
    print(f"Node embeddings shape: {node_embeddings.shape}")
