#!/usr/bin/env python3
"""
数据验证脚本 - 检查预处理后的多模态数据质量
"""

import os
import sys
import pickle
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np
import yaml
from PIL import Image
import matplotlib.pyplot as plt

# 添加项目路径
current_dir = Path(__file__).parent.parent
sys.path.append(str(current_dir))


class ProcessedDataValidator:
    """
    预处理数据验证器
    """
    
    def __init__(self, data_dir: str):
        """
        初始化验证器
        
        Args:
            data_dir: 预处理后的数据目录
        """
        self.data_dir = Path(data_dir)
        self.stats = {
            'total_samples': 0,
            'valid_samples': 0,
            'invalid_samples': 0,
            'splits': {},
            'classes': {},
            'errors': []
        }
    
    def validate_directory_structure(self) -> bool:
        """
        验证目录结构
        
        Returns:
            是否符合预期结构
        """
        print("🔍 验证目录结构...")
        
        if not self.data_dir.exists():
            print(f"❌ 数据目录不存在: {self.data_dir}")
            return False
        
        # 检查必需的分割目录
        required_splits = ['train', 'val', 'test']
        missing_splits = []
        
        for split in required_splits:
            split_dir = self.data_dir / split
            if not split_dir.exists():
                missing_splits.append(split)
            else:
                print(f"✅ 找到 {split} 目录")
        
        if missing_splits:
            print(f"⚠️ 缺少分割目录: {missing_splits}")
        
        # 检查数据集信息文件
        info_file = self.data_dir / 'dataset_info.yaml'
        if info_file.exists():
            print("✅ 找到数据集信息文件")
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    self.dataset_info = yaml.safe_load(f)
                print(f"   总样本数: {self.dataset_info.get('total_samples', 'unknown')}")
                print(f"   类别数: {len(self.dataset_info.get('classes', {}))}")
            except Exception as e:
                print(f"⚠️ 读取数据集信息文件失败: {e}")
                self.dataset_info = {}
        else:
            print("⚠️ 未找到数据集信息文件")
            self.dataset_info = {}
        
        return len(missing_splits) == 0
    
    def validate_samples(self) -> Dict:
        """
        验证样本数据
        
        Returns:
            验证统计信息
        """
        print("\n🔍 验证样本数据...")
        
        for split in ['train', 'val', 'test']:
            split_dir = self.data_dir / split
            if not split_dir.exists():
                continue
            
            print(f"\n📊 验证 {split} 数据集...")
            split_stats = self._validate_split(split_dir, split)
            self.stats['splits'][split] = split_stats
        
        return self.stats
    
    def _validate_split(self, split_dir: Path, split_name: str) -> Dict:
        """
        验证单个分割的数据
        
        Args:
            split_dir: 分割目录
            split_name: 分割名称
            
        Returns:
            分割统计信息
        """
        split_stats = {
            'total_samples': 0,
            'valid_samples': 0,
            'invalid_samples': 0,
            'classes': {},
            'errors': []
        }
        
        # 遍历类别目录
        for class_dir in split_dir.iterdir():
            if not class_dir.is_dir():
                continue
            
            class_name = class_dir.name
            print(f"  验证类别: {class_name}")
            
            class_stats = self._validate_class(class_dir, class_name, split_name)
            split_stats['classes'][class_name] = class_stats
            split_stats['total_samples'] += class_stats['total_samples']
            split_stats['valid_samples'] += class_stats['valid_samples']
            split_stats['invalid_samples'] += class_stats['invalid_samples']
            split_stats['errors'].extend(class_stats['errors'])
        
        print(f"  {split_name} 总计: {split_stats['valid_samples']}/{split_stats['total_samples']} 有效样本")
        
        return split_stats
    
    def _validate_class(self, class_dir: Path, class_name: str, split_name: str) -> Dict:
        """
        验证单个类别的数据
        
        Args:
            class_dir: 类别目录
            class_name: 类别名称
            split_name: 分割名称
            
        Returns:
            类别统计信息
        """
        class_stats = {
            'total_samples': 0,
            'valid_samples': 0,
            'invalid_samples': 0,
            'errors': []
        }
        
        # 查找所有pkl文件
        pkl_files = list(class_dir.glob("*.pkl"))
        class_stats['total_samples'] = len(pkl_files)
        
        for pkl_file in pkl_files:
            sample_id = pkl_file.stem
            
            try:
                # 验证pkl文件
                is_valid = self._validate_sample(pkl_file, class_dir, sample_id)
                
                if is_valid:
                    class_stats['valid_samples'] += 1
                else:
                    class_stats['invalid_samples'] += 1
                    
            except Exception as e:
                class_stats['invalid_samples'] += 1
                error_msg = f"{split_name}/{class_name}/{sample_id}: {e}"
                class_stats['errors'].append(error_msg)
        
        print(f"    {class_stats['valid_samples']}/{class_stats['total_samples']} 有效样本")
        
        return class_stats
    
    def _validate_sample(self, pkl_file: Path, class_dir: Path, sample_id: str) -> bool:
        """
        验证单个样本
        
        Args:
            pkl_file: pkl文件路径
            class_dir: 类别目录
            sample_id: 样本ID
            
        Returns:
            是否有效
        """
        # 检查对应的图像文件
        img_file = class_dir / f"{sample_id}.png"
        if not img_file.exists():
            raise ValueError(f"缺少图像文件: {img_file}")
        
        # 验证pkl文件
        try:
            with open(pkl_file, 'rb') as f:
                data = pickle.load(f)
        except Exception as e:
            raise ValueError(f"无法读取pkl文件: {e}")
        
        # 检查数据结构
        required_keys = ['sequence', 'graph', 'sample_id', 'class_name']
        for key in required_keys:
            if key not in data:
                raise ValueError(f"缺少必需字段: {key}")
        
        # 验证序列数据
        sequence_data = data['sequence']
        if not isinstance(sequence_data, dict):
            raise ValueError("序列数据格式错误")
        
        seq_required_keys = ['input_ids', 'attention_mask', 'time_features', 'raw_features']
        for key in seq_required_keys:
            if key not in sequence_data:
                raise ValueError(f"序列数据缺少字段: {key}")
        
        # 验证图数据
        graph_data = data['graph']
        if not isinstance(graph_data, dict):
            raise ValueError("图数据格式错误")
        
        graph_required_keys = ['adj_matrix', 'node_features', 'global_features']
        for key in graph_required_keys:
            if key not in graph_data:
                raise ValueError(f"图数据缺少字段: {key}")
        
        # 验证图像文件
        try:
            img = Image.open(img_file)
            if img.size[0] == 0 or img.size[1] == 0:
                raise ValueError("图像尺寸为0")
        except Exception as e:
            raise ValueError(f"图像文件损坏: {e}")
        
        return True
    
    def generate_report(self, output_file: str = None) -> str:
        """
        生成验证报告
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            报告内容
        """
        print("\n📋 生成验证报告...")
        
        report_lines = []
        report_lines.append("# 多模态数据验证报告")
        report_lines.append("=" * 50)
        
        # 总体统计
        total_samples = sum(split['total_samples'] for split in self.stats['splits'].values())
        valid_samples = sum(split['valid_samples'] for split in self.stats['splits'].values())
        invalid_samples = sum(split['invalid_samples'] for split in self.stats['splits'].values())
        
        report_lines.append(f"\n## 总体统计")
        report_lines.append(f"- 总样本数: {total_samples}")
        report_lines.append(f"- 有效样本数: {valid_samples}")
        report_lines.append(f"- 无效样本数: {invalid_samples}")
        report_lines.append(f"- 有效率: {valid_samples/total_samples*100:.1f}%" if total_samples > 0 else "- 有效率: 0%")
        
        # 各分割统计
        report_lines.append(f"\n## 各分割统计")
        for split_name, split_stats in self.stats['splits'].items():
            report_lines.append(f"\n### {split_name.upper()} 数据集")
            report_lines.append(f"- 总样本数: {split_stats['total_samples']}")
            report_lines.append(f"- 有效样本数: {split_stats['valid_samples']}")
            report_lines.append(f"- 无效样本数: {split_stats['invalid_samples']}")
            
            # 各类别统计
            if split_stats['classes']:
                report_lines.append(f"\n#### 类别分布")
                for class_name, class_stats in split_stats['classes'].items():
                    valid_rate = class_stats['valid_samples'] / class_stats['total_samples'] * 100 if class_stats['total_samples'] > 0 else 0
                    report_lines.append(f"- {class_name}: {class_stats['valid_samples']}/{class_stats['total_samples']} ({valid_rate:.1f}%)")
        
        # 错误统计
        all_errors = []
        for split_stats in self.stats['splits'].values():
            all_errors.extend(split_stats['errors'])
        
        if all_errors:
            report_lines.append(f"\n## 错误详情 (共{len(all_errors)}个)")
            for i, error in enumerate(all_errors[:20]):  # 只显示前20个错误
                report_lines.append(f"{i+1}. {error}")
            
            if len(all_errors) > 20:
                report_lines.append(f"... 还有 {len(all_errors) - 20} 个错误")
        
        # 建议
        report_lines.append(f"\n## 建议")
        if invalid_samples == 0:
            report_lines.append("✅ 所有样本都有效，数据质量良好")
        elif invalid_samples / total_samples < 0.1:
            report_lines.append("⚠️ 少量无效样本，建议检查并修复")
        else:
            report_lines.append("❌ 大量无效样本，建议重新预处理数据")
        
        report_content = "\n".join(report_lines)
        
        # 保存报告
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"✅ 验证报告已保存到: {output_file}")
        
        return report_content
    
    def visualize_statistics(self, output_dir: str = None):
        """
        可视化统计信息
        
        Args:
            output_dir: 输出目录
        """
        print("\n📊 生成可视化图表...")
        
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
        
        # 1. 各分割样本数分布
        splits = list(self.stats['splits'].keys())
        split_counts = [self.stats['splits'][split]['valid_samples'] for split in splits]
        
        plt.figure(figsize=(10, 6))
        plt.bar(splits, split_counts)
        plt.title('各分割有效样本数分布')
        plt.xlabel('数据分割')
        plt.ylabel('样本数')
        
        for i, count in enumerate(split_counts):
            plt.text(i, count + max(split_counts) * 0.01, str(count), ha='center')
        
        if output_dir:
            plt.savefig(output_dir / 'split_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 2. 各类别样本数分布
        all_classes = set()
        for split_stats in self.stats['splits'].values():
            all_classes.update(split_stats['classes'].keys())
        
        if all_classes:
            class_counts = {}
            for class_name in all_classes:
                total_count = 0
                for split_stats in self.stats['splits'].values():
                    if class_name in split_stats['classes']:
                        total_count += split_stats['classes'][class_name]['valid_samples']
                class_counts[class_name] = total_count
            
            plt.figure(figsize=(12, 6))
            classes = list(class_counts.keys())
            counts = list(class_counts.values())
            
            plt.bar(classes, counts)
            plt.title('各类别有效样本数分布')
            plt.xlabel('类别')
            plt.ylabel('样本数')
            plt.xticks(rotation=45)
            
            for i, count in enumerate(counts):
                plt.text(i, count + max(counts) * 0.01, str(count), ha='center')
            
            plt.tight_layout()
            
            if output_dir:
                plt.savefig(output_dir / 'class_distribution.png', dpi=300, bbox_inches='tight')
            plt.show()


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='验证预处理后的多模态数据')
    parser.add_argument('--data-dir', type=str, required=True, help='预处理后的数据目录')
    parser.add_argument('--output-dir', type=str, help='输出目录（用于保存报告和图表）')
    parser.add_argument('--report-file', type=str, help='验证报告文件路径')
    parser.add_argument('--no-viz', action='store_true', help='不生成可视化图表')
    
    args = parser.parse_args()
    
    # 创建验证器
    validator = ProcessedDataValidator(args.data_dir)
    
    # 验证目录结构
    structure_valid = validator.validate_directory_structure()
    
    if not structure_valid:
        print("❌ 目录结构验证失败")
        return 1
    
    # 验证样本数据
    stats = validator.validate_samples()
    
    # 生成报告
    report_file = args.report_file or (args.output_dir and f"{args.output_dir}/validation_report.md")
    report = validator.generate_report(report_file)
    
    # 打印报告
    print("\n" + report)
    
    # 生成可视化
    if not args.no_viz:
        try:
            validator.visualize_statistics(args.output_dir)
        except Exception as e:
            print(f"⚠️ 生成可视化图表失败: {e}")
    
    # 返回状态
    total_samples = sum(split['total_samples'] for split in stats['splits'].values())
    valid_samples = sum(split['valid_samples'] for split in stats['splits'].values())
    
    if total_samples == 0:
        print("\n❌ 未找到任何样本")
        return 1
    elif valid_samples == total_samples:
        print("\n✅ 所有样本都有效")
        return 0
    elif valid_samples / total_samples >= 0.9:
        print("\n⚠️ 大部分样本有效，但存在少量问题")
        return 0
    else:
        print("\n❌ 存在大量无效样本")
        return 1


if __name__ == "__main__":
    exit(main())
