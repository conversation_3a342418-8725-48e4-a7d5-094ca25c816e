"""
Train a diffusion model on images.
"""

import argparse
import sys
sys.path.append(".")

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from diffusion_v2 import dist_util, logger
from diffusion_v2.image_datasets import load_data
from diffusion_v2.resample import create_named_schedule_sampler
from diffusion_v2.script_util import (
    model_and_diffusion_defaults,
    create_model_and_diffusion,
    args_to_dict,
    add_dict_to_argparser,
)
from diffusion_v2.train_util import TrainLoop
import os
os.environ['CUDA_VISIBLE_DEVICES'] = '3'

def main():
    args = create_argparser().parse_args()

    dist_util.setup_dist()

    # Configure logger with custom directory if specified
    if args.log_dir:
        os.makedirs(args.log_dir, exist_ok=True)
        logger.configure(dir=args.log_dir)
    else:
        logger.configure()

    logger.log("creating model and diffusion...")
    model, diffusion = create_model_and_diffusion(
        **args_to_dict(args, model_and_diffusion_defaults().keys())
    )
    model.to(dist_util.dev())
    schedule_sampler = create_named_schedule_sampler(args.schedule_sampler, diffusion)

    logger.log("creating data loader...")
    data = load_data(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        image_size=args.image_size,
        class_cond=args.class_cond,
        use_imagefolder=args.use_imagefolder,
    )

    logger.log("training...")
    TrainLoop(
        model=model,
        diffusion=diffusion,
        data=data,
        batch_size=args.batch_size,
        microbatch=args.microbatch,
        lr=args.lr,
        ema_rate=args.ema_rate,
        log_interval=args.log_interval,
        save_interval=args.save_interval,
        resume_checkpoint=args.resume_checkpoint,
        use_fp16=args.use_fp16,
        fp16_scale_growth=args.fp16_scale_growth,
        schedule_sampler=schedule_sampler,
        weight_decay=args.weight_decay,
        lr_anneal_steps=args.lr_anneal_steps,
        log_dir=args.log_dir,
    ).run_loop()


def create_argparser():
    defaults = dict(
        data_dir="",
        schedule_sampler= "uniform",
        # use_k1 = "True",
        lr=1e-4,
        weight_decay=0.0,
        lr_anneal_steps=0,
        batch_size=64,
        microbatch=-1,  # -1 disables microbatches
        ema_rate="0.9999",  # comma-separated list of EMA values
        log_interval=10,
        save_interval=2000,
        resume_checkpoint="",
        use_fp16=False,
        fp16_scale_growth=1e-3,
        log_dir="",  # Custom log directory for weights and logs
        use_imagefolder=True,  # Use ImageFolder format for class labels
    )
    # save_interval = 10000,
    defaults.update(model_and_diffusion_defaults())
    parser = argparse.ArgumentParser()
    add_dict_to_argparser(parser, defaults)
    return parser


if __name__ == "__main__":
    main()
