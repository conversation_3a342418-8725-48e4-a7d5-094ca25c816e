"""
多模态少样本数据集 - 整合序列、图像和图三种模态数据
"""

import os
import pickle
import numpy as np
import torch
from torch.utils.data import Dataset
from typing import Dict, List, Tuple, Optional
import random
from PIL import Image
import torchvision.transforms as transforms

from .pcap_processor import PCAPProcessor
from .sequence_processor import SequenceProcessor
from .graph_processor import GraphProcessor


class MultiModalFewShotDataset(Dataset):
    """
    多模态少样本数据集，支持序列、图像和图三种模态
    """
    
    def __init__(self, config: Dict, data_root: str, split: str = 'train'):
        """
        初始化多模态数据集
        
        Args:
            config: 配置字典
            data_root: 数据根目录
            split: 数据集分割 ('train', 'val', 'test')
        """
        self.config = config
        self.data_root = data_root
        self.split = split
        
        # 少样本学习参数
        self.n_way = config['protonet']['n_way']
        self.k_shot = config['protonet']['k_shot']
        self.k_query = config['protonet']['k_query']
        self.episodes_num = config['protonet']['episodes_num']
        
        # 数据处理器
        self.pcap_processor = PCAPProcessor(config['data'])
        self.sequence_processor = SequenceProcessor(config['model']['sequence_branch'])
        self.graph_processor = GraphProcessor(config['model']['graph_branch'])
        
        # 图像变换
        self.image_transforms = self._build_image_transforms()
        
        # 加载数据
        self.data_dict = self._load_data()
        self.classes = list(self.data_dict.keys())
        
        print(f"Loaded {split} dataset with {len(self.classes)} classes")
        for cls, samples in self.data_dict.items():
            print(f"  Class {cls}: {len(samples)} samples")
    
    def _build_image_transforms(self) -> transforms.Compose:
        """
        构建图像变换
        
        Returns:
            图像变换组合
        """
        img_size = self.config['data']['img_size']
        
        if self.split == 'train':
            # 训练时使用数据增强
            transform_list = [
                transforms.Resize((img_size, img_size)),
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomRotation(degrees=10),
                transforms.ColorJitter(
                    brightness=0.2,
                    contrast=0.2,
                    saturation=0.2,
                    hue=0.1
                ),
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                )
            ]
        else:
            # 验证/测试时不使用增强
            transform_list = [
                transforms.Resize((img_size, img_size)),
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                )
            ]
        
        return transforms.Compose(transform_list)
    
    def _load_data(self) -> Dict[str, List[Dict]]:
        """
        加载多模态数据
        
        Returns:
            按类别组织的数据字典
        """
        data_dict = {}
        split_dir = os.path.join(self.data_root, self.split)
        
        if not os.path.exists(split_dir):
            raise FileNotFoundError(f"Data directory not found: {split_dir}")
        
        # 遍历类别目录
        for class_name in os.listdir(split_dir):
            class_dir = os.path.join(split_dir, class_name)
            if not os.path.isdir(class_dir):
                continue
            
            class_samples = []
            
            # 遍历样本文件
            for sample_file in os.listdir(class_dir):
                sample_path = os.path.join(class_dir, sample_file)
                
                if sample_file.endswith('.pkl'):
                    # PCAP处理后的数据
                    try:
                        with open(sample_path, 'rb') as f:
                            pcap_data = pickle.load(f)
                        
                        # 查找对应的图像文件
                        img_file = sample_file.replace('.pkl', '.png')
                        img_path = os.path.join(class_dir, img_file)
                        
                        if os.path.exists(img_path):
                            sample_data = {
                                'pcap_data': pcap_data,
                                'image_path': img_path,
                                'class_name': class_name,
                                'sample_id': sample_file
                            }
                            class_samples.append(sample_data)
                    
                    except Exception as e:
                        print(f"Error loading {sample_path}: {e}")
                        continue
            
            if class_samples:
                data_dict[class_name] = class_samples
        
        return data_dict
    
    def __len__(self) -> int:
        """
        返回数据集大小（episode数量）
        """
        return self.episodes_num
    
    def __getitem__(self, index: int) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        获取一个episode的数据
        
        Args:
            index: episode索引
            
        Returns:
            (support_x, support_y, query_x, query_y) 四元组
        """
        # 随机选择N个类别
        selected_classes = random.sample(self.classes, self.n_way)
        
        support_data = []
        support_labels = []
        query_data = []
        query_labels = []
        
        for class_idx, class_name in enumerate(selected_classes):
            class_samples = self.data_dict[class_name]
            
            # 确保有足够的样本
            total_needed = self.k_shot + self.k_query
            if len(class_samples) < total_needed:
                # 如果样本不够，进行重复采样
                selected_samples = random.choices(class_samples, k=total_needed)
            else:
                selected_samples = random.sample(class_samples, total_needed)
            
            # 分割支持集和查询集
            support_samples = selected_samples[:self.k_shot]
            query_samples = selected_samples[self.k_shot:self.k_shot + self.k_query]
            
            # 处理支持集
            for sample in support_samples:
                multimodal_data = self._process_sample(sample)
                support_data.append(multimodal_data)
                support_labels.append(class_idx)
            
            # 处理查询集
            for sample in query_samples:
                multimodal_data = self._process_sample(sample)
                query_data.append(multimodal_data)
                query_labels.append(class_idx)
        
        # 转换为张量
        support_x = self._collate_multimodal_data(support_data)
        support_y = torch.tensor(support_labels, dtype=torch.long)
        query_x = self._collate_multimodal_data(query_data)
        query_y = torch.tensor(query_labels, dtype=torch.long)
        
        return support_x, support_y, query_x, query_y

    def _process_sample(self, sample: Dict) -> Dict:
        """
        处理单个样本，提取三种模态的特征

        Args:
            sample: 样本数据字典

        Returns:
            多模态特征字典
        """
        pcap_data = sample['pcap_data']
        image_path = sample['image_path']

        # 处理序列数据
        sequence_data = self.sequence_processor.process_sequence(pcap_data['sequence'])

        # 处理图数据
        graph_data = self.graph_processor.process_graph(pcap_data['graph'])

        # 处理图像数据
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.image_transforms(image)

        return {
            'sequence': sequence_data,
            'graph': graph_data,
            'image': image_tensor
        }

    def _collate_multimodal_data(self, data_list: List[Dict]) -> Dict[str, torch.Tensor]:
        """
        整理多模态数据为批次格式

        Args:
            data_list: 多模态数据列表

        Returns:
            批次化的多模态数据
        """
        batch_size = len(data_list)

        # 序列数据批次化
        sequence_batch = {
            'input_ids': torch.stack([torch.tensor(d['sequence']['input_ids']) for d in data_list]),
            'attention_mask': torch.stack([torch.tensor(d['sequence']['attention_mask']) for d in data_list]),
            'position_ids': torch.stack([torch.tensor(d['sequence']['position_ids']) for d in data_list]),
            'time_features': torch.stack([torch.tensor(d['sequence']['time_features']) for d in data_list]),
            'raw_features': torch.stack([torch.tensor(d['sequence']['raw_features']) for d in data_list])
        }

        # 图数据批次化
        graph_batch = {
            'x': torch.stack([d['graph']['x'] for d in data_list]),
            'edge_index': [d['graph']['edge_index'] for d in data_list],  # 保持列表形式
            'edge_attr': [d['graph']['edge_attr'] for d in data_list],
            'adj_matrix': torch.stack([d['graph']['adj_matrix'] for d in data_list]),
            'global_features': torch.stack([d['graph']['global_features'] for d in data_list]),
            'batch': torch.cat([d['graph']['batch'] + i * d['graph']['x'].size(0)
                               for i, d in enumerate(data_list)])
        }

        # 图像数据批次化
        image_batch = torch.stack([d['image'] for d in data_list])

        return {
            'sequence': sequence_batch,
            'graph': graph_batch,
            'image': image_batch
        }

    def get_contrastive_data(self, batch_size: int = 64) -> Dict[str, torch.Tensor]:
        """
        获取对比学习数据

        Args:
            batch_size: 批次大小

        Returns:
            对比学习数据批次
        """
        contrastive_data = []

        # 随机采样样本
        all_samples = []
        for class_samples in self.data_dict.values():
            all_samples.extend(class_samples)

        selected_samples = random.sample(all_samples, min(batch_size, len(all_samples)))

        # 为每个样本创建两个增强视图
        for sample in selected_samples:
            # 第一个视图
            view1 = self._process_sample(sample)
            contrastive_data.append(view1)

            # 第二个视图（应用不同的增强）
            view2 = self._process_sample_with_augmentation(sample)
            contrastive_data.append(view2)

        return self._collate_multimodal_data(contrastive_data)

    def _process_sample_with_augmentation(self, sample: Dict) -> Dict:
        """
        处理样本并应用数据增强

        Args:
            sample: 样本数据字典

        Returns:
            增强后的多模态特征字典
        """
        pcap_data = sample['pcap_data']
        image_path = sample['image_path']

        # 序列数据增强
        sequence_augmented = self.sequence_processor.create_data_augmentation(pcap_data['sequence'])
        sequence_data = self.sequence_processor.process_sequence(random.choice(sequence_augmented))

        # 图数据增强
        graph_augmented = self.graph_processor.apply_graph_augmentation(pcap_data['graph'])
        graph_data = self.graph_processor.process_graph(random.choice(graph_augmented))

        # 图像数据（已在transforms中包含增强）
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.image_transforms(image)

        return {
            'sequence': sequence_data,
            'graph': graph_data,
            'image': image_tensor
        }


class MultiModalContrastiveDataset(Dataset):
    """
    专门用于对比学习的多模态数据集
    """

    def __init__(self, config: Dict, data_root: str, split: str = 'train'):
        """
        初始化对比学习数据集

        Args:
            config: 配置字典
            data_root: 数据根目录
            split: 数据集分割
        """
        self.config = config
        self.data_root = data_root
        self.split = split

        # 对比学习参数
        self.batch_size = config['simclr']['batch_size']
        self.n_views = config['simclr']['n_views']

        # 数据处理器
        self.pcap_processor = PCAPProcessor(config['data'])
        self.sequence_processor = SequenceProcessor(config['model']['sequence_branch'])
        self.graph_processor = GraphProcessor(config['model']['graph_branch'])

        # 图像变换（强增强用于对比学习）
        self.image_transforms = self._build_contrastive_transforms()

        # 加载所有样本
        self.samples = self._load_all_samples()

        print(f"Loaded contrastive dataset with {len(self.samples)} samples")

    def _build_contrastive_transforms(self) -> transforms.Compose:
        """
        构建对比学习的图像变换（强增强）

        Returns:
            图像变换组合
        """
        img_size = self.config['data']['img_size']

        return transforms.Compose([
            transforms.Resize((img_size, img_size)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomVerticalFlip(p=0.2),
            transforms.RandomRotation(degrees=30),
            transforms.ColorJitter(
                brightness=0.4,
                contrast=0.4,
                saturation=0.4,
                hue=0.2
            ),
            transforms.RandomGrayscale(p=0.2),
            transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 2.0)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])

    def _load_all_samples(self) -> List[Dict]:
        """
        加载所有样本

        Returns:
            样本列表
        """
        all_samples = []
        split_dir = os.path.join(self.data_root, self.split)

        for class_name in os.listdir(split_dir):
            class_dir = os.path.join(split_dir, class_name)
            if not os.path.isdir(class_dir):
                continue

            for sample_file in os.listdir(class_dir):
                if sample_file.endswith('.pkl'):
                    sample_path = os.path.join(class_dir, sample_file)
                    img_file = sample_file.replace('.pkl', '.png')
                    img_path = os.path.join(class_dir, img_file)

                    if os.path.exists(img_path):
                        try:
                            with open(sample_path, 'rb') as f:
                                pcap_data = pickle.load(f)

                            sample_data = {
                                'pcap_data': pcap_data,
                                'image_path': img_path,
                                'class_name': class_name,
                                'sample_id': sample_file
                            }
                            all_samples.append(sample_data)

                        except Exception as e:
                            print(f"Error loading {sample_path}: {e}")
                            continue

        return all_samples

    def __len__(self) -> int:
        """
        返回数据集大小
        """
        return len(self.samples)

    def __getitem__(self, index: int) -> List[Dict]:
        """
        获取一个样本的多个视图

        Args:
            index: 样本索引

        Returns:
            多个视图的列表
        """
        sample = self.samples[index]
        views = []

        # 生成多个视图
        for _ in range(self.n_views):
            view_data = self._process_sample_with_strong_augmentation(sample)
            views.append(view_data)

        return views

    def _process_sample_with_strong_augmentation(self, sample: Dict) -> Dict:
        """
        处理样本并应用强数据增强

        Args:
            sample: 样本数据字典

        Returns:
            强增强后的多模态特征字典
        """
        pcap_data = sample['pcap_data']
        image_path = sample['image_path']

        # 序列数据强增强
        sequence_augmented = self.sequence_processor.create_data_augmentation(pcap_data['sequence'])
        sequence_data = self.sequence_processor.process_sequence(random.choice(sequence_augmented))

        # 图数据强增强
        graph_augmented = self.graph_processor.apply_graph_augmentation(pcap_data['graph'])
        graph_data = self.graph_processor.process_graph(random.choice(graph_augmented))

        # 图像数据强增强
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.image_transforms(image)

        return {
            'sequence': sequence_data,
            'graph': graph_data,
            'image': image_tensor
        }
