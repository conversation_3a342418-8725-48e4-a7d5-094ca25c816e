# 多视图三分支对比元学习框架 (MultiModalProtoSimCLR)

## 项目概述

基于现有ProtoSimCLR框架，实现了一个多模态三分支网络架构，用于网络入侵检测的少样本学习任务。

## 架构设计

### 三分支网络结构

1. **序列分支 (Sequential Branch)**
   - 输入：原始清洗后的PCAP文件序列数据
   - 特征提取器：Transformer编码器
   - 输出：序列特征嵌入

2. **图像分支 (Image Branch)**
   - 输入：现有的RGB图像表示
   - 特征提取器：基于ResNet的SimCLR编码器（保持现有实现）
   - 输出：视觉特征嵌入

3. **图分支 (Graph Branch)**
   - 输入：从PCAP文件转换的图表示
   - 特征提取器：图卷积网络(GCN)
   - 输出：图特征嵌入

### 融合机制

- **注意力融合**：使用多头注意力机制融合三个分支的特征
- **门控融合**：通过门控机制动态调整各分支的贡献权重
- **残差连接**：保持各分支特征的独立性

### 训练策略

保持现有的两阶段训练策略：
1. **Stage 1**: 对比学习预训练 - 三个分支同时参与InfoNCE损失计算
2. **Stage 2**: 元学习微调 - 使用融合特征进行原型网络训练

## 文件结构

```
MultiModalProtoSimCLR/
├── README.md                    # 项目说明
├── config/                      # 配置文件
│   ├── multimodal_config.yaml   # 多模态配置
│   └── fusion_strategies.yaml   # 融合策略配置
├── data/                        # 数据处理模块
│   ├── __init__.py
│   ├── pcap_processor.py        # PCAP数据处理
│   ├── sequence_processor.py    # 序列数据处理
│   ├── graph_processor.py       # 图数据处理
│   └── multimodal_dataset.py    # 多模态数据集
├── models/                      # 模型实现
│   ├── __init__.py
│   ├── encoders/                # 编码器模块
│   │   ├── __init__.py
│   │   ├── transformer_encoder.py  # Transformer编码器
│   │   ├── gcn_encoder.py          # GCN编码器
│   │   └── resnet_encoder.py       # ResNet编码器(复用现有)
│   ├── fusion/                  # 融合模块
│   │   ├── __init__.py
│   │   ├── attention_fusion.py     # 注意力融合
│   │   ├── gate_fusion.py          # 门控融合
│   │   └── concat_fusion.py        # 简单拼接融合
│   └── multimodal_protonet.py   # 多模态原型网络
├── training/                    # 训练模块
│   ├── __init__.py
│   ├── multimodal_trainer.py    # 多模态训练器
│   └── contrastive_trainer.py   # 对比学习训练器
├── utils/                       # 工具函数
│   ├── __init__.py
│   ├── metrics.py              # 评估指标
│   └── visualization.py        # 可视化工具
└── experiments/                 # 实验脚本
    ├── train_multimodal.py     # 多模态训练脚本
    └── evaluate_multimodal.py  # 多模态评估脚本
```

## 核心创新点

1. **多模态特征学习**：同时利用序列、图像和图三种数据表示
2. **自适应融合机制**：根据任务动态调整各模态的贡献
3. **统一对比学习**：三个分支共享对比学习目标
4. **端到端训练**：整个多模态系统可以端到端优化

## 使用方法

### 1. 数据准备
```bash
python data/pcap_processor.py --input_dir /path/to/pcap --output_dir /path/to/processed
```

### 2. 训练模型
```bash
python experiments/train_multimodal.py --config config/multimodal_config.yaml
```

### 3. 评估模型
```bash
python experiments/evaluate_multimodal.py --model_path /path/to/model --test_data /path/to/test
```

## 技术特点

- **模块化设计**：各个组件可独立测试和替换
- **配置驱动**：通过YAML配置文件控制所有超参数
- **兼容性**：与现有ProtoSimCLR框架完全兼容
- **可扩展性**：易于添加新的模态或融合策略

## 依赖要求

- PyTorch >= 1.8.0
- torch-geometric >= 2.0.0
- transformers >= 4.0.0
- numpy >= 1.19.0
- scikit-learn >= 0.24.0
- PyYAML >= 5.4.0
