"""
多模态系统测试脚本
"""

import torch
import yaml
import numpy as np
from models.multimodal_protonet import MultiModalProtoNet


def create_test_config():
    """
    创建测试配置
    """
    config = {
        'data': {
            'sequence_length': 128,
            'graph_max_nodes': 50,
            'img_size': 32,
            'img_channels': 3,
            'num_classes': 5
        },
        'model': {
            'sequence_branch': {
                'vocab_size': 1000,
                'embedding_dim': 128,
                'num_heads': 4,
                'num_layers': 3,
                'hidden_dim': 512,
                'dropout': 0.1,
                'max_position_embeddings': 128,
                'output_dim': 256,
                'pooling_strategy': 'cls'
            },
            'image_branch': {
                'base_model': 'resnet18',
                'pretrained': False,
                'freeze_backbone': False,
                'simclr_dim': 128,
                'output_dim': 256
            },
            'graph_branch': {
                'input_dim': 6,
                'hidden_dims': [64, 128],
                'num_layers': 3,
                'dropout': 0.2,
                'activation': 'relu',
                'output_dim': 256,
                'pooling': 'global_mean',
                'use_attention': False,
                'attention_heads': 4
            },
            'fusion': {
                'strategy': 'attention',
                'fusion_dim': 768,
                'attention_heads': 8,
                'dropout': 0.1,
                'output_dim': 512
            }
        },
        'protonet': {
            'n_way': 5,
            'k_shot': 3,
            'k_query': 12,
            'episodes_num': 100,
            'temperature': 10.0,
            'distance_metric': 'cosine'
        },
        'simclr': {
            'temperature': 0.07,
            'batch_size': 32,
            'n_views': 2,
            'projection_dim': 128
        },
        'training': {
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'epochs': 100,
            'learning_rate': 0.001
        },
        'loss_weights': {
            'contrastive_loss': 1.0,
            'prototype_loss': 1.0,
            'fusion_regularization': 0.1
        }
    }
    
    return config


def create_test_data(config, batch_size=4):
    """
    创建测试数据
    """
    seq_len = config['data']['sequence_length']
    num_nodes = config['data']['graph_max_nodes']
    img_size = config['data']['img_size']
    
    multimodal_data = {
        'sequence': {
            'input_ids': torch.randint(0, config['model']['sequence_branch']['vocab_size'], 
                                     (batch_size, seq_len)),
            'attention_mask': torch.ones(batch_size, seq_len),
            'position_ids': torch.arange(seq_len).unsqueeze(0).expand(batch_size, -1),
            'time_features': torch.randn(batch_size, seq_len, 3),
            'raw_features': torch.randn(batch_size, seq_len, 8)
        },
        'image': torch.randn(batch_size, 3, img_size, img_size),
        'graph': {
            'x': torch.randn(batch_size, num_nodes, 6),
            'edge_index': torch.randint(0, num_nodes, (2, batch_size * 20)),
            'edge_attr': torch.randn(batch_size * 20, 2),
            'adj_matrix': torch.randint(0, 2, (batch_size, num_nodes, num_nodes)).float(),
            'global_features': torch.randn(batch_size, 15),
            'batch': torch.zeros(batch_size * num_nodes, dtype=torch.long)
        }
    }
    
    return multimodal_data


def test_model_initialization():
    """
    测试模型初始化
    """
    print("🧪 Testing model initialization...")
    
    config = create_test_config()
    model = MultiModalProtoNet(config)
    
    # 检查模型结构
    param_count = model.get_parameter_count()
    print(f"✅ Model initialized successfully")
    print(f"   Total parameters: {param_count['total_parameters']:,}")
    print(f"   Trainable parameters: {param_count['total_trainable']:,}")
    print(f"   Sequence encoder: {param_count['sequence_encoder_trainable']:,}")
    print(f"   Image encoder: {param_count['image_encoder_trainable']:,}")
    print(f"   Graph encoder: {param_count['graph_encoder_trainable']:,}")
    print(f"   Fusion module: {param_count['fusion_module_trainable']:,}")
    
    return model, config


def test_forward_pass(model, config):
    """
    测试前向传播
    """
    print("\n🧪 Testing forward pass...")
    
    batch_size = 4
    multimodal_data = create_test_data(config, batch_size)
    
    # 测试基本前向传播
    with torch.no_grad():
        features = model(multimodal_data)
        print(f"✅ Forward pass successful")
        print(f"   Output shape: {features.shape}")
        print(f"   Expected shape: [{batch_size}, {model.output_dim}]")
        
        # 检查特征是否归一化
        norms = torch.norm(features, dim=1)
        print(f"   Feature norms (should be ~1.0): {norms.mean().item():.4f} ± {norms.std().item():.4f}")


def test_individual_branches(model, config):
    """
    测试各个分支的独立功能
    """
    print("\n🧪 Testing individual branches...")
    
    batch_size = 4
    multimodal_data = create_test_data(config, batch_size)
    
    with torch.no_grad():
        # 测试各分支特征提取
        branch_features = model.get_individual_branch_features(multimodal_data)
        
        print(f"✅ Individual branches working")
        for branch_name, features in branch_features.items():
            print(f"   {branch_name} features shape: {features.shape}")
            norms = torch.norm(features, dim=1)
            print(f"   {branch_name} norms: {norms.mean().item():.4f} ± {norms.std().item():.4f}")


def test_fusion_analysis(model, config):
    """
    测试融合分析功能
    """
    print("\n🧪 Testing fusion analysis...")
    
    batch_size = 4
    multimodal_data = create_test_data(config, batch_size)
    
    with torch.no_grad():
        analysis = model.get_fusion_analysis(multimodal_data)
        
        print(f"✅ Fusion analysis working")
        print(f"   Branch similarities:")
        for pair, similarity in analysis['branch_similarities'].items():
            print(f"     {pair}: {similarity:.4f}")
        
        if 'fusion_weights' in analysis:
            weights = analysis['fusion_weights']
            print(f"   Fusion weights: {weights}")


def test_episode_forward(model, config):
    """
    测试episode前向传播（元学习）
    """
    print("\n🧪 Testing episode forward pass...")
    
    n_way = config['protonet']['n_way']
    k_shot = config['protonet']['k_shot']
    k_query = config['protonet']['k_query']
    
    # 创建支持集和查询集数据
    support_data = create_test_data(config, n_way * k_shot)
    query_data = create_test_data(config, n_way * k_query)
    
    with torch.no_grad():
        logits = model.episode_forward(support_data, query_data)
        
        print(f"✅ Episode forward pass successful")
        print(f"   Logits shape: {logits.shape}")
        print(f"   Expected shape: [{n_way * k_query}, {n_way}]")
        
        # 检查logits的分布
        print(f"   Logits mean: {logits.mean().item():.4f}")
        print(f"   Logits std: {logits.std().item():.4f}")


def test_contrastive_loss(model, config):
    """
    测试对比学习损失
    """
    print("\n🧪 Testing contrastive loss...")
    
    batch_size = 8  # 4个样本，每个2个视图
    multimodal_data = create_test_data(config, batch_size)
    
    with torch.no_grad():
        features = model(multimodal_data)
        cl_logits, cl_labels = model.info_nce_loss(features)
        
        print(f"✅ Contrastive loss computation successful")
        print(f"   Contrastive logits shape: {cl_logits.shape}")
        print(f"   Contrastive labels shape: {cl_labels.shape}")
        print(f"   Expected labels: all zeros for positive pairs first")
        print(f"   Actual labels (first 5): {cl_labels[:5]}")


def test_fusion_strategies(config):
    """
    测试不同的融合策略
    """
    print("\n🧪 Testing different fusion strategies...")
    
    strategies = ['attention', 'gate', 'concat']
    batch_size = 4
    
    for strategy in strategies:
        print(f"\n   Testing {strategy} fusion...")
        
        # 修改配置
        test_config = config.copy()
        test_config['model']['fusion']['strategy'] = strategy
        
        try:
            model = MultiModalProtoNet(test_config)
            multimodal_data = create_test_data(test_config, batch_size)
            
            with torch.no_grad():
                features = model(multimodal_data)
                print(f"   ✅ {strategy} fusion working, output shape: {features.shape}")
                
        except Exception as e:
            print(f"   ❌ {strategy} fusion failed: {e}")


def test_parameter_freezing(model):
    """
    测试参数冻结功能
    """
    print("\n🧪 Testing parameter freezing...")
    
    # 测试冻结和解冻
    branches = ['sequence', 'image', 'graph']
    
    for branch in branches:
        # 冻结分支
        model.freeze_branch(branch)
        
        # 检查参数是否被冻结
        if branch == 'sequence':
            encoder = model.sequence_encoder
        elif branch == 'image':
            encoder = model.image_encoder
        else:
            encoder = model.graph_encoder
        
        frozen_count = sum(1 for p in encoder.parameters() if not p.requires_grad)
        total_count = sum(1 for p in encoder.parameters())
        
        print(f"   {branch} branch: {frozen_count}/{total_count} parameters frozen")
        
        # 解冻分支
        model.unfreeze_branch(branch)
        
        unfrozen_count = sum(1 for p in encoder.parameters() if p.requires_grad)
        print(f"   {branch} branch: {unfrozen_count}/{total_count} parameters unfrozen")


def main():
    """
    主测试函数
    """
    print("🚀 Starting MultiModal ProtoSimCLR System Tests\n")
    
    try:
        # 1. 测试模型初始化
        model, config = test_model_initialization()
        
        # 2. 测试前向传播
        test_forward_pass(model, config)
        
        # 3. 测试各个分支
        test_individual_branches(model, config)
        
        # 4. 测试融合分析
        test_fusion_analysis(model, config)
        
        # 5. 测试episode前向传播
        test_episode_forward(model, config)
        
        # 6. 测试对比学习损失
        test_contrastive_loss(model, config)
        
        # 7. 测试不同融合策略
        test_fusion_strategies(config)
        
        # 8. 测试参数冻结
        test_parameter_freezing(model)
        
        print("\n🎉 All tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
