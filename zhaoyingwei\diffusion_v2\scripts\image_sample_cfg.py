"""
Generate image samples using Classifier-Free Guidance (CFG).
This script supports both conditional and unconditional generation with CFG.
"""

import argparse
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import torch as th
import torch.distributed as dist
from PIL import Image

from diffusion_v2 import dist_util, logger
from diffusion_v2.script_util import (
    model_and_diffusion_defaults,
    create_model_and_diffusion,
    add_dict_to_argparser,
    args_to_dict,
)


def classifier_free_guidance_sample(
    model, diffusion, shape, class_labels=None, guidance_scale=1.0, 
    clip_denoised=True, model_kwargs=None, device=None, progress=False
):
    """
    Sample using Classifier-Free Guidance.
    
    :param model: the model to sample from.
    :param diffusion: the diffusion process.
    :param shape: the shape of the samples.
    :param class_labels: class labels for conditional generation.
    :param guidance_scale: guidance scale for CFG (1.0 = no guidance).
    :param clip_denoised: whether to clip denoised samples.
    :param model_kwargs: additional model arguments.
    :param device: device to run on.
    :param progress: whether to show progress.
    :return: generated samples.
    """
    if device is None:
        device = next(model.parameters()).device
    if model_kwargs is None:
        model_kwargs = {}
        
    # Add class labels if provided
    if class_labels is not None:
        model_kwargs["y"] = class_labels.to(device)
    
    def cfg_model_fn(x, t, **kwargs):
        """Model function with Classifier-Free Guidance"""
        if guidance_scale == 1.0 or "y" not in kwargs:
            # No guidance or unconditional
            return model(x, t, **kwargs)
        
        # Conditional prediction
        y_cond = kwargs["y"]
        cond_pred = model(x, t, **kwargs)
        
        # Unconditional prediction
        kwargs_uncond = dict(kwargs)
        kwargs_uncond["y"] = th.full_like(y_cond, -1)  # Unconditional label
        uncond_pred = model(x, t, **kwargs_uncond)
        
        # Apply guidance
        guided_pred = uncond_pred + guidance_scale * (cond_pred - uncond_pred)
        return guided_pred
    
    # Sample using the guided model
    return diffusion.p_sample_loop(
        cfg_model_fn,
        shape,
        clip_denoised=clip_denoised,
        model_kwargs=model_kwargs,
        device=device,
        progress=progress,
    )


def main():
    args = create_argparser().parse_args()

    dist_util.setup_dist()
    logger.configure()

    logger.log("creating model and diffusion...")
    model, diffusion = create_model_and_diffusion(
        **args_to_dict(args, model_and_diffusion_defaults().keys())
    )
    model.load_state_dict(
        dist_util.load_state_dict(args.model_path, map_location="cpu")
    )
    model.to(dist_util.dev())
    model.eval()

    logger.log("sampling...")
    all_images = []
    all_labels = []
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    sample_count = 0
    while sample_count < args.num_samples:
        batch_size = min(args.batch_size, args.num_samples - sample_count)
        
        # Prepare class labels
        class_labels = None
        if args.class_cond:
            if args.specific_class >= 0:
                # Generate specific class
                class_labels = th.full((batch_size,), args.specific_class, dtype=th.long)
            else:
                # Random classes
                class_labels = th.randint(
                    low=0, high=args.num_classes, size=(batch_size,), dtype=th.long
                )
        
        logger.log(f"Generating batch {sample_count // args.batch_size + 1}...")
        
        # Generate samples with CFG
        samples = classifier_free_guidance_sample(
            model=model,
            diffusion=diffusion,
            shape=(batch_size, 3, args.image_size, args.image_size),
            class_labels=class_labels,
            guidance_scale=args.guidance_scale,
            clip_denoised=args.clip_denoised,
            device=dist_util.dev(),
            progress=True,
        )
        
        # Convert to numpy and store
        samples = ((samples + 1) * 127.5).clamp(0, 255).to(th.uint8)
        samples = samples.permute(0, 2, 3, 1).cpu().numpy()
        
        all_images.append(samples)
        if class_labels is not None:
            all_labels.append(class_labels.cpu().numpy())
        
        # Save individual images
        for i, sample in enumerate(samples):
            img = Image.fromarray(sample)
            if class_labels is not None:
                class_id = class_labels[i].item()
                filename = f"sample_{sample_count + i:06d}_class_{class_id}.png"
            else:
                filename = f"sample_{sample_count + i:06d}.png"
            img.save(os.path.join(args.output_dir, filename))
        
        sample_count += batch_size
        logger.log(f"Generated {sample_count}/{args.num_samples} samples")

    # Save all samples as numpy arrays
    all_images = np.concatenate(all_images, axis=0)
    np.savez(
        os.path.join(args.output_dir, "samples.npz"),
        images=all_images,
        labels=np.concatenate(all_labels, axis=0) if all_labels else None,
    )
    
    logger.log(f"Sampling complete. Saved {len(all_images)} samples to {args.output_dir}")


def create_argparser():
    defaults = dict(
        clip_denoised=True,
        num_samples=16,
        batch_size=4,
        use_ddim=False,
        model_path="",
        output_dir="./samples",
        guidance_scale=1.0,  # CFG guidance scale
        specific_class=-1,   # Generate specific class (-1 for random)
        num_classes=10,      # Number of classes in dataset
    )
    defaults.update(model_and_diffusion_defaults())
    parser = argparse.ArgumentParser()
    add_dict_to_argparser(parser, defaults)
    return parser


if __name__ == "__main__":
    main()
