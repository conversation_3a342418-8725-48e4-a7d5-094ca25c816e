"""
多模态训练器 - 实现两阶段训练策略
"""

import os
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from typing import Dict, List, Optional, Tuple
import numpy as np
from tqdm import tqdm

from ..models.multimodal_protonet import MultiModalProtoNet
from ..data.multimodal_dataset import MultiModalFewShotDataset, MultiModalContrastiveDataset


class MultiModalTrainer:
    """
    多模态训练器，实现两阶段训练策略
    """
    
    def __init__(self, config: Dict, model: MultiModalProtoNet):
        """
        初始化训练器
        
        Args:
            config: 配置字典
            model: 多模态原型网络模型
        """
        self.config = config
        self.model = model
        self.device = torch.device(config['training']['device'])
        
        # 训练参数
        self.stage1_epochs = config['training']['stage1_epochs']
        self.stage2_epochs = config['training']['stage2_epochs']
        self.learning_rate = config['training']['learning_rate']
        self.weight_decay = config['training']['weight_decay']
        self.gradient_clip_norm = config['training']['gradient_clip_norm']
        
        # 损失权重
        self.loss_weights = config['loss_weights']
        
        # 优化器和调度器
        self.optimizer = None
        self.scheduler = None
        
        # 日志记录
        self.writer = None
        self.save_dir = config['logging']['save_dir']
        self.log_dir = config['logging']['log_dir']
        
        # 创建保存目录
        os.makedirs(self.save_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 移动模型到设备
        self.model.to(self.device)
        
        print(f"✅ MultiModalTrainer initialized")
        print(f"✅ Device: {self.device}")
        print(f"✅ Stage 1 epochs: {self.stage1_epochs}")
        print(f"✅ Stage 2 epochs: {self.stage2_epochs}")
    
    def setup_optimizer(self, stage: str = 'stage1'):
        """
        设置优化器和学习率调度器
        
        Args:
            stage: 训练阶段 ('stage1' 或 'stage2')
        """
        # 获取可训练参数
        trainable_params = [p for p in self.model.parameters() if p.requires_grad]
        
        # 创建优化器
        optimizer_type = self.config['training']['optimizer']
        if optimizer_type == 'adam':
            self.optimizer = optim.Adam(
                trainable_params,
                lr=self.learning_rate,
                weight_decay=self.weight_decay
            )
        elif optimizer_type == 'sgd':
            self.optimizer = optim.SGD(
                trainable_params,
                lr=self.learning_rate,
                momentum=self.config['training']['momentum'],
                weight_decay=self.weight_decay
            )
        else:
            raise ValueError(f"Unknown optimizer: {optimizer_type}")
        
        # 创建学习率调度器
        scheduler_config = self.config['training']['lr_scheduler']
        if scheduler_config['type'] == 'cosine':
            total_epochs = self.stage1_epochs if stage == 'stage1' else self.stage2_epochs
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=total_epochs,
                eta_min=scheduler_config['eta_min']
            )
        elif scheduler_config['type'] == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config.get('step_size', 30),
                gamma=scheduler_config.get('gamma', 0.1)
            )
        elif scheduler_config['type'] == 'plateau':
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=scheduler_config.get('factor', 0.5),
                patience=scheduler_config.get('patience', 10)
            )
        
        print(f"✅ Optimizer and scheduler setup for {stage}")
    
    def setup_tensorboard(self, stage: str):
        """
        设置TensorBoard日志记录
        
        Args:
            stage: 训练阶段
        """
        log_path = os.path.join(self.log_dir, f"{stage}_{int(time.time())}")
        self.writer = SummaryWriter(log_path)
        print(f"✅ TensorBoard logging to: {log_path}")
    
    def stage1_contrastive_pretraining(self, train_dataset: MultiModalContrastiveDataset,
                                     val_dataset: Optional[MultiModalContrastiveDataset] = None):
        """
        阶段1：对比学习预训练
        
        Args:
            train_dataset: 训练数据集
            val_dataset: 验证数据集（可选）
        """
        print("\n🚀 Starting Stage 1: Contrastive Pre-training")
        
        # 设置优化器和日志
        self.setup_optimizer('stage1')
        self.setup_tensorboard('stage1')
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True,
            num_workers=self.config['training']['num_workers'],
            collate_fn=self._contrastive_collate_fn
        )
        
        val_loader = None
        if val_dataset is not None:
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.config['training']['batch_size'],
                shuffle=False,
                num_workers=self.config['training']['num_workers'],
                collate_fn=self._contrastive_collate_fn
            )
        
        # 损失函数
        criterion = nn.CrossEntropyLoss()
        
        # 训练循环
        best_val_loss = float('inf')
        
        for epoch in range(self.stage1_epochs):
            print(f"\nEpoch {epoch + 1}/{self.stage1_epochs}")
            
            # 训练
            train_loss = self._train_contrastive_epoch(train_loader, criterion, epoch)
            
            # 验证
            val_loss = None
            if val_loader is not None:
                val_loss = self._validate_contrastive_epoch(val_loader, criterion, epoch)
            
            # 学习率调度
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                self.scheduler.step(val_loss if val_loss is not None else train_loss)
            else:
                self.scheduler.step()
            
            # 保存最佳模型
            if val_loss is not None and val_loss < best_val_loss:
                best_val_loss = val_loss
                self.save_checkpoint(epoch, 'stage1_best.pth')
            
            # 定期保存
            if (epoch + 1) % self.config['logging']['save_freq'] == 0:
                self.save_checkpoint(epoch, f'stage1_epoch_{epoch + 1}.pth')
        
        # 保存最终模型
        self.save_checkpoint(self.stage1_epochs - 1, 'stage1_final.pth')
        print("✅ Stage 1 completed")
    
    def stage2_meta_learning(self, train_dataset: MultiModalFewShotDataset,
                           val_dataset: Optional[MultiModalFewShotDataset] = None):
        """
        阶段2：元学习微调
        
        Args:
            train_dataset: 训练数据集
            val_dataset: 验证数据集（可选）
        """
        print("\n🚀 Starting Stage 2: Meta-Learning Fine-tuning")
        
        # 设置优化器和日志
        self.setup_optimizer('stage2')
        self.setup_tensorboard('stage2')
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=1,  # 每个episode作为一个batch
            shuffle=True,
            num_workers=self.config['training']['num_workers']
        )
        
        val_loader = None
        if val_dataset is not None:
            val_loader = DataLoader(
                val_dataset,
                batch_size=1,
                shuffle=False,
                num_workers=self.config['training']['num_workers']
            )
        
        # 损失函数
        criterion = nn.CrossEntropyLoss()
        
        # 训练循环
        best_val_acc = 0.0
        
        for epoch in range(self.stage2_epochs):
            print(f"\nEpoch {epoch + 1}/{self.stage2_epochs}")
            
            # 训练
            train_loss, train_acc = self._train_meta_epoch(train_loader, criterion, epoch)
            
            # 验证
            val_loss, val_acc = None, None
            if val_loader is not None:
                val_loss, val_acc = self._validate_meta_epoch(val_loader, criterion, epoch)
            
            # 学习率调度
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                self.scheduler.step(val_loss if val_loss is not None else train_loss)
            else:
                self.scheduler.step()
            
            # 保存最佳模型
            if val_acc is not None and val_acc > best_val_acc:
                best_val_acc = val_acc
                self.save_checkpoint(epoch, 'stage2_best.pth')
            
            # 定期保存
            if (epoch + 1) % self.config['logging']['save_freq'] == 0:
                self.save_checkpoint(epoch, f'stage2_epoch_{epoch + 1}.pth')
        
        # 保存最终模型
        self.save_checkpoint(self.stage2_epochs - 1, 'stage2_final.pth')
        print("✅ Stage 2 completed")
    
    def _train_contrastive_epoch(self, train_loader: DataLoader, criterion: nn.Module, epoch: int) -> float:
        """
        训练一个对比学习epoch
        
        Args:
            train_loader: 训练数据加载器
            criterion: 损失函数
            epoch: 当前epoch
            
        Returns:
            平均训练损失
        """
        self.model.train()
        total_loss = 0.0
        num_batches = len(train_loader)
        
        progress_bar = tqdm(train_loader, desc=f'Training Epoch {epoch + 1}')
        
        for batch_idx, batch_views in enumerate(progress_bar):
            # batch_views是一个包含多个视图的列表
            all_features = []
            
            for view_data in batch_views:
                features = self.model(view_data)
                all_features.append(features)
            
            # 拼接所有视图的特征
            all_features = torch.cat(all_features, dim=0)
            
            # 计算对比学习损失
            cl_logits, cl_labels = self.model.info_nce_loss(all_features)
            contrastive_loss = criterion(cl_logits, cl_labels)
            
            # 计算融合正则化损失
            reg_loss = self.model.compute_fusion_regularization()
            
            # 总损失
            total_loss_batch = (
                self.loss_weights['contrastive_loss'] * contrastive_loss +
                self.loss_weights['fusion_regularization'] * reg_loss
            )
            
            # 反向传播
            self.optimizer.zero_grad()
            total_loss_batch.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.gradient_clip_norm)
            
            self.optimizer.step()
            
            # 记录损失
            total_loss += total_loss_batch.item()
            
            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{total_loss_batch.item():.4f}',
                'CL_Loss': f'{contrastive_loss.item():.4f}',
                'Reg_Loss': f'{reg_loss.item():.4f}'
            })
            
            # 记录到TensorBoard
            global_step = epoch * num_batches + batch_idx
            if batch_idx % self.config['logging']['log_freq'] == 0:
                self.writer.add_scalar('Train/ContrastiveLoss', contrastive_loss.item(), global_step)
                self.writer.add_scalar('Train/RegularizationLoss', reg_loss.item(), global_step)
                self.writer.add_scalar('Train/TotalLoss', total_loss_batch.item(), global_step)
                self.writer.add_scalar('Train/LearningRate', self.optimizer.param_groups[0]['lr'], global_step)
        
        avg_loss = total_loss / num_batches
        print(f"Training Loss: {avg_loss:.4f}")
        
        return avg_loss

    def _validate_contrastive_epoch(self, val_loader: DataLoader, criterion: nn.Module, epoch: int) -> float:
        """
        验证一个对比学习epoch

        Args:
            val_loader: 验证数据加载器
            criterion: 损失函数
            epoch: 当前epoch

        Returns:
            平均验证损失
        """
        self.model.eval()
        total_loss = 0.0
        num_batches = len(val_loader)

        with torch.no_grad():
            for batch_idx, batch_views in enumerate(val_loader):
                # 处理多视图数据
                all_features = []

                for view_data in batch_views:
                    features = self.model(view_data)
                    all_features.append(features)

                # 拼接所有视图的特征
                all_features = torch.cat(all_features, dim=0)

                # 计算对比学习损失
                cl_logits, cl_labels = self.model.info_nce_loss(all_features)
                contrastive_loss = criterion(cl_logits, cl_labels)

                # 计算融合正则化损失
                reg_loss = self.model.compute_fusion_regularization()

                # 总损失
                total_loss_batch = (
                    self.loss_weights['contrastive_loss'] * contrastive_loss +
                    self.loss_weights['fusion_regularization'] * reg_loss
                )

                total_loss += total_loss_batch.item()

        avg_loss = total_loss / num_batches
        print(f"Validation Loss: {avg_loss:.4f}")

        # 记录到TensorBoard
        self.writer.add_scalar('Val/ContrastiveLoss', avg_loss, epoch)

        return avg_loss

    def _train_meta_epoch(self, train_loader: DataLoader, criterion: nn.Module, epoch: int) -> Tuple[float, float]:
        """
        训练一个元学习epoch

        Args:
            train_loader: 训练数据加载器
            criterion: 损失函数
            epoch: 当前epoch

        Returns:
            (平均训练损失, 平均训练准确率)
        """
        self.model.train()
        total_loss = 0.0
        total_acc = 0.0
        num_episodes = len(train_loader)

        progress_bar = tqdm(train_loader, desc=f'Meta Training Epoch {epoch + 1}')

        for episode_idx, (support_x, support_y, query_x, query_y) in enumerate(progress_bar):
            # 移动数据到设备
            support_x = self._move_to_device(support_x)
            query_x = self._move_to_device(query_x)
            query_y = query_y.to(self.device)

            # Episode前向传播
            logits = self.model.episode_forward(support_x, query_x)

            # 计算原型损失
            prototype_loss = criterion(logits, query_y)

            # 计算融合正则化损失
            reg_loss = self.model.compute_fusion_regularization()

            # 总损失
            total_loss_episode = (
                self.loss_weights['prototype_loss'] * prototype_loss +
                self.loss_weights['fusion_regularization'] * reg_loss
            )

            # 反向传播
            self.optimizer.zero_grad()
            total_loss_episode.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.gradient_clip_norm)

            self.optimizer.step()

            # 计算准确率
            with torch.no_grad():
                pred = logits.argmax(dim=1)
                accuracy = (pred == query_y).float().mean().item()

            # 记录指标
            total_loss += total_loss_episode.item()
            total_acc += accuracy

            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{total_loss_episode.item():.4f}',
                'Acc': f'{accuracy:.4f}',
                'Proto_Loss': f'{prototype_loss.item():.4f}',
                'Reg_Loss': f'{reg_loss.item():.4f}'
            })

            # 记录到TensorBoard
            global_step = epoch * num_episodes + episode_idx
            if episode_idx % self.config['logging']['log_freq'] == 0:
                self.writer.add_scalar('Train/PrototypeLoss', prototype_loss.item(), global_step)
                self.writer.add_scalar('Train/RegularizationLoss', reg_loss.item(), global_step)
                self.writer.add_scalar('Train/TotalLoss', total_loss_episode.item(), global_step)
                self.writer.add_scalar('Train/Accuracy', accuracy, global_step)
                self.writer.add_scalar('Train/LearningRate', self.optimizer.param_groups[0]['lr'], global_step)

        avg_loss = total_loss / num_episodes
        avg_acc = total_acc / num_episodes

        print(f"Training Loss: {avg_loss:.4f}, Training Accuracy: {avg_acc:.4f}")

        return avg_loss, avg_acc

    def _validate_meta_epoch(self, val_loader: DataLoader, criterion: nn.Module, epoch: int) -> Tuple[float, float]:
        """
        验证一个元学习epoch

        Args:
            val_loader: 验证数据加载器
            criterion: 损失函数
            epoch: 当前epoch

        Returns:
            (平均验证损失, 平均验证准确率)
        """
        self.model.eval()
        total_loss = 0.0
        total_acc = 0.0
        num_episodes = len(val_loader)

        with torch.no_grad():
            for episode_idx, (support_x, support_y, query_x, query_y) in enumerate(val_loader):
                # 移动数据到设备
                support_x = self._move_to_device(support_x)
                query_x = self._move_to_device(query_x)
                query_y = query_y.to(self.device)

                # Episode前向传播
                logits = self.model.episode_forward(support_x, query_x)

                # 计算损失
                prototype_loss = criterion(logits, query_y)
                reg_loss = self.model.compute_fusion_regularization()

                total_loss_episode = (
                    self.loss_weights['prototype_loss'] * prototype_loss +
                    self.loss_weights['fusion_regularization'] * reg_loss
                )

                # 计算准确率
                pred = logits.argmax(dim=1)
                accuracy = (pred == query_y).float().mean().item()

                # 记录指标
                total_loss += total_loss_episode.item()
                total_acc += accuracy

        avg_loss = total_loss / num_episodes
        avg_acc = total_acc / num_episodes

        print(f"Validation Loss: {avg_loss:.4f}, Validation Accuracy: {avg_acc:.4f}")

        # 记录到TensorBoard
        self.writer.add_scalar('Val/PrototypeLoss', avg_loss, epoch)
        self.writer.add_scalar('Val/Accuracy', avg_acc, epoch)

        return avg_loss, avg_acc

    def _move_to_device(self, data):
        """
        将数据移动到指定设备

        Args:
            data: 输入数据（可能是字典、张量或列表）

        Returns:
            移动到设备后的数据
        """
        if isinstance(data, dict):
            return {key: self._move_to_device(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._move_to_device(item) for item in data]
        elif isinstance(data, torch.Tensor):
            return data.to(self.device)
        else:
            return data

    def _contrastive_collate_fn(self, batch):
        """
        对比学习数据的collate函数

        Args:
            batch: 批次数据

        Returns:
            整理后的批次数据
        """
        # batch中每个元素是一个包含多个视图的列表
        # 需要将所有视图整理成统一格式

        num_views = len(batch[0])  # 每个样本的视图数
        batch_size = len(batch)

        # 按视图组织数据
        view_batches = []
        for view_idx in range(num_views):
            view_data = []
            for sample_idx in range(batch_size):
                view_data.append(batch[sample_idx][view_idx])

            # 整理当前视图的数据
            collated_view = self._collate_multimodal_data(view_data)
            view_batches.append(collated_view)

        return view_batches

    def _collate_multimodal_data(self, data_list):
        """
        整理多模态数据

        Args:
            data_list: 多模态数据列表

        Returns:
            整理后的多模态数据
        """
        if not data_list:
            return {}

        # 序列数据整理
        sequence_batch = {
            'input_ids': torch.stack([d['sequence']['input_ids'] for d in data_list]),
            'attention_mask': torch.stack([d['sequence']['attention_mask'] for d in data_list]),
            'position_ids': torch.stack([d['sequence']['position_ids'] for d in data_list]),
            'time_features': torch.stack([d['sequence']['time_features'] for d in data_list]),
            'raw_features': torch.stack([d['sequence']['raw_features'] for d in data_list])
        }

        # 图数据整理
        graph_batch = {
            'x': torch.stack([d['graph']['x'] for d in data_list]),
            'adj_matrix': torch.stack([d['graph']['adj_matrix'] for d in data_list]),
            'global_features': torch.stack([d['graph']['global_features'] for d in data_list])
        }

        # 图像数据整理
        image_batch = torch.stack([d['image'] for d in data_list])

        return {
            'sequence': sequence_batch,
            'graph': graph_batch,
            'image': image_batch
        }

    def save_checkpoint(self, epoch: int, filename: str):
        """
        保存检查点

        Args:
            epoch: 当前epoch
            filename: 保存文件名
        """
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'config': self.config
        }

        save_path = os.path.join(self.save_dir, filename)
        torch.save(checkpoint, save_path)
        print(f"✅ Checkpoint saved: {save_path}")

    def load_checkpoint(self, checkpoint_path: str) -> int:
        """
        加载检查点

        Args:
            checkpoint_path: 检查点文件路径

        Returns:
            加载的epoch数
        """
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])

        if self.optimizer and 'optimizer_state_dict' in checkpoint:
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        if self.scheduler and 'scheduler_state_dict' in checkpoint and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        epoch = checkpoint.get('epoch', 0)
        print(f"✅ Checkpoint loaded from: {checkpoint_path}, epoch: {epoch}")

        return epoch

    def close(self):
        """
        关闭训练器，清理资源
        """
        if self.writer:
            self.writer.close()
        print("✅ Trainer closed")


if __name__ == "__main__":
    # 测试代码
    import yaml
    from ..models.multimodal_protonet import MultiModalProtoNet

    # 加载配置
    with open('../config/multimodal_config.yaml', 'r') as f:
        config = yaml.safe_load(f)

    # 创建模型和训练器
    model = MultiModalProtoNet(config)
    trainer = MultiModalTrainer(config, model)

    print("✅ MultiModalTrainer test completed")
