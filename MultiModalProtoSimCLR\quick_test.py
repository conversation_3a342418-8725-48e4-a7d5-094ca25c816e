"""
快速测试脚本 - 验证多模态系统的核心功能
"""

import torch
import numpy as np

def test_basic_functionality():
    """测试基本功能"""
    print("🚀 Starting Quick Test of MultiModal ProtoSimCLR System")
    
    # 测试配置
    config = {
        'data': {
            'sequence_length': 64,
            'graph_max_nodes': 20,
            'img_size': 32,
            'img_channels': 3,
            'num_classes': 5
        },
        'model': {
            'sequence_branch': {
                'vocab_size': 100,
                'embedding_dim': 64,
                'num_heads': 4,
                'num_layers': 2,
                'hidden_dim': 256,
                'dropout': 0.1,
                'max_position_embeddings': 64,
                'output_dim': 128,
                'pooling_strategy': 'cls'
            },
            'image_branch': {
                'base_model': 'resnet18',
                'pretrained': False,
                'freeze_backbone': False,
                'simclr_dim': 64,
                'output_dim': 128
            },
            'graph_branch': {
                'input_dim': 4,
                'hidden_dims': [32, 64],
                'num_layers': 2,
                'dropout': 0.1,
                'activation': 'relu',
                'output_dim': 128,
                'pooling': 'global_mean',
                'use_attention': False,
                'attention_heads': 2
            },
            'fusion': {
                'strategy': 'concat',
                'fusion_dim': 384,
                'attention_heads': 4,
                'dropout': 0.1,
                'output_dim': 256,
                'input_dim': 128  # 添加输入维度配置
            }
        },
        'protonet': {
            'n_way': 3,
            'k_shot': 2,
            'k_query': 5,
            'episodes_num': 10,
            'temperature': 10.0,
            'distance_metric': 'cosine'
        },
        'simclr': {
            'temperature': 0.07,
            'batch_size': 8,
            'n_views': 2,
            'projection_dim': 64
        },
        'training': {
            'device': 'cpu',
            'epochs': 10,
            'learning_rate': 0.001
        },
        'loss_weights': {
            'contrastive_loss': 1.0,
            'prototype_loss': 1.0,
            'fusion_regularization': 0.1
        }
    }
    
    try:
        # 1. 测试模型创建
        print("\n1️⃣ Testing model creation...")
        from models.multimodal_protonet import MultiModalProtoNet
        model = MultiModalProtoNet(config)
        print("✅ Model created successfully")
        
        # 2. 测试数据创建
        print("\n2️⃣ Testing data creation...")
        batch_size = 4
        seq_len = config['data']['sequence_length']
        num_nodes = config['data']['graph_max_nodes']
        img_size = config['data']['img_size']
        
        multimodal_data = {
            'sequence': {
                'input_ids': torch.randint(0, config['model']['sequence_branch']['vocab_size'], 
                                         (batch_size, seq_len)),
                'attention_mask': torch.ones(batch_size, seq_len),
                'position_ids': torch.arange(seq_len).unsqueeze(0).expand(batch_size, -1),
                'time_features': torch.randn(batch_size, seq_len, 3),
                'raw_features': torch.randn(batch_size, seq_len, 8)
            },
            'image': torch.randn(batch_size, 3, img_size, img_size),
            'graph': {
                'x': torch.randn(batch_size, num_nodes, 4),
                'edge_index': torch.randint(0, num_nodes, (2, batch_size * 10)),
                'edge_attr': torch.randn(batch_size * 10, 2),
                'adj_matrix': torch.randint(0, 2, (batch_size, num_nodes, num_nodes)).float(),
                'global_features': torch.randn(batch_size, 15),
                'batch': torch.zeros(batch_size * num_nodes, dtype=torch.long)
            }
        }
        print("✅ Test data created successfully")
        
        # 3. 测试前向传播
        print("\n3️⃣ Testing forward pass...")
        with torch.no_grad():
            features = model(multimodal_data)
            print(f"✅ Forward pass successful, output shape: {features.shape}")
        
        # 4. 测试各分支独立功能
        print("\n4️⃣ Testing individual branches...")
        with torch.no_grad():
            branch_features = model.get_individual_branch_features(multimodal_data)
            for branch_name, features in branch_features.items():
                print(f"   {branch_name}: {features.shape}")
            print("✅ Individual branches working")
        
        # 5. 测试episode前向传播
        print("\n5️⃣ Testing episode forward...")
        n_way = config['protonet']['n_way']
        k_shot = config['protonet']['k_shot']
        k_query = config['protonet']['k_query']
        
        # 创建支持集和查询集
        support_data = {
            'sequence': {
                'input_ids': torch.randint(0, config['model']['sequence_branch']['vocab_size'], 
                                         (n_way * k_shot, seq_len)),
                'attention_mask': torch.ones(n_way * k_shot, seq_len),
                'position_ids': torch.arange(seq_len).unsqueeze(0).expand(n_way * k_shot, -1),
                'time_features': torch.randn(n_way * k_shot, seq_len, 3),
                'raw_features': torch.randn(n_way * k_shot, seq_len, 8)
            },
            'image': torch.randn(n_way * k_shot, 3, img_size, img_size),
            'graph': {
                'x': torch.randn(n_way * k_shot, num_nodes, 4),
                'adj_matrix': torch.randint(0, 2, (n_way * k_shot, num_nodes, num_nodes)).float(),
                'global_features': torch.randn(n_way * k_shot, 15)
            }
        }
        
        query_data = {
            'sequence': {
                'input_ids': torch.randint(0, config['model']['sequence_branch']['vocab_size'], 
                                         (n_way * k_query, seq_len)),
                'attention_mask': torch.ones(n_way * k_query, seq_len),
                'position_ids': torch.arange(seq_len).unsqueeze(0).expand(n_way * k_query, -1),
                'time_features': torch.randn(n_way * k_query, seq_len, 3),
                'raw_features': torch.randn(n_way * k_query, seq_len, 8)
            },
            'image': torch.randn(n_way * k_query, 3, img_size, img_size),
            'graph': {
                'x': torch.randn(n_way * k_query, num_nodes, 4),
                'adj_matrix': torch.randint(0, 2, (n_way * k_query, num_nodes, num_nodes)).float(),
                'global_features': torch.randn(n_way * k_query, 15)
            }
        }
        
        with torch.no_grad():
            logits = model.episode_forward(support_data, query_data)
            print(f"✅ Episode forward successful, logits shape: {logits.shape}")
        
        # 6. 测试对比学习损失
        print("\n6️⃣ Testing contrastive loss...")
        with torch.no_grad():
            cl_features = torch.randn(8, model.output_dim)  # 4个样本，每个2个视图
            cl_logits, cl_labels = model.info_nce_loss(cl_features)
            print(f"✅ Contrastive loss computed, logits: {cl_logits.shape}, labels: {cl_labels.shape}")
        
        # 7. 测试参数统计
        print("\n7️⃣ Testing parameter counting...")
        param_count = model.get_parameter_count()
        print(f"✅ Total parameters: {param_count['total_parameters']:,}")
        print(f"   Trainable: {param_count['total_trainable']:,}")
        
        # 8. 测试不同融合策略
        print("\n8️⃣ Testing different fusion strategies...")
        strategies = ['attention', 'gate', 'concat']
        for strategy in strategies:
            try:
                test_config = config.copy()
                test_config['model']['fusion']['strategy'] = strategy
                test_model = MultiModalProtoNet(test_config)
                
                with torch.no_grad():
                    test_features = test_model(multimodal_data)
                    print(f"   ✅ {strategy} fusion: {test_features.shape}")
            except Exception as e:
                print(f"   ❌ {strategy} fusion failed: {e}")
        
        print("\n🎉 All tests passed successfully!")
        print("✅ MultiModal ProtoSimCLR system is working correctly")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n🚀 System is ready for use!")
        print("📖 See USAGE.md for detailed instructions")
    else:
        print("\n⚠️ Please check the errors above and fix them before proceeding")
