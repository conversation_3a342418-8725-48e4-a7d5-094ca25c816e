# Diffusion V2 项目总结

## 🎯 项目目标

基于原有的 `improved_diffusion` 代码，实现两个关键功能：
1. **可指定权重保存路径** - 在训练脚本中指定自定义的日志和权重保存目录
2. **类别引导训练 (CG)** - 使用 ImageFolder 格式数据集进行 Classifier Guidance 训练

## ✅ 完成的功能

### 1. 自定义权重保存路径 ✅
- **修改文件**: `train_util.py`, `scripts/image_train.py`
- **新增参数**: `--log_dir` 
- **功能**: 
  - 支持指定自定义目录保存模型权重和训练日志
  - 自动创建目录结构
  - 兼容原有的默认行为

**使用方法**:
```bash
python scripts/image_train.py --log_dir ./my_experiment_logs
```

### 2. ImageFolder 数据集支持 ✅
- **修改文件**: `image_datasets.py`
- **新增参数**: `--use_imagefolder`
- **功能**:
  - 自动从目录结构提取类别标签
  - 支持标准 ImageFolder 格式
  - 自动生成类别到索引的映射

**数据集格式**:
```
dataset/
├── class1/
│   ├── image1.jpg
│   └── image2.jpg
├── class2/
│   └── image1.jpg
└── class3/
    └── image1.jpg
```

### 3. Classifier Guidance (CG) 实现 ✅
- **新增文件**: `classifier.py`
- **功能**:
  - 独立的分类器网络，支持时间步嵌入
  - 在不同噪声水平下预测类别概率
  - 支持注意力机制和残差连接

**CG 原理**:
- 训练：分别训练扩散模型和分类器
- 采样：使用分类器梯度引导生成过程

### 4. CG 采样支持 ✅
- **修改文件**: `gaussian_diffusion.py`
- **新增参数**: `cond_fn` 支持
- **功能**:
  - 在采样过程中应用分类器梯度
  - 支持可调节的引导强度
  - 与原有采样流程完全兼容

### 5. 分类器训练脚本 ✅
- **新增文件**: `scripts/classifier_train.py`
- **功能**:
  - 在噪声图像上训练分类器
  - 支持不同时间步的训练
  - 自动保存检查点和日志

### 6. CG 采样脚本 ✅
- **新增文件**: `scripts/classifier_sample.py`
- **功能**:
  - 使用训练好的分类器进行引导采样
  - 支持指定类别或随机类别生成
  - 可调节分类器引导强度

## 🧪 测试结果

运行 `test_simple.py` 的测试结果：
- ✅ **CFG 训练逻辑**: 标签丢弃机制正常工作
- ✅ **UNet CFG 处理**: 正确处理无条件标签 (-1)
- ✅ **扩散过程**: 前向扩散过程正常
- ✅ **ImageFolder 逻辑**: 类别提取逻辑正确
- ⚠️ **MPI 导入**: 由于 Windows 环境限制，MPI 相关功能无法测试

## 📋 新增参数说明

### 训练参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--log_dir` | `""` | 自定义日志和权重保存目录 |
| `--use_imagefolder` | `True` | 使用 ImageFolder 格式数据集 |

### 分类器训练参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--iterations` | `100000` | 分类器训练迭代次数 |
| `--lr` | `3e-4` | 分类器学习率 |
| `--classifier_scale` | `1.0` | 分类器引导强度 |

### 采样参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--classifier_scale` | `1.0` | CG 引导强度 (越大引导越强) |
| `--specific_class` | `-1` | 指定生成类别 (-1=随机) |
| `--output_dir` | `./samples` | 生成样本保存目录 |

## 🚀 使用示例

### 完整训练流程
```bash
# 1. 训练类别条件扩散模型
python scripts/train_class_conditional.py \
    --data_dir /path/to/imagefolder/dataset \
    --num_classes 10 \
    --log_dir ./logs/diffusion_model \
    --batch_size 16 \
    --save_interval 1000

# 2. 训练分类器
python scripts/classifier_train.py \
    --data_dir /path/to/imagefolder/dataset \
    --num_classes 10 \
    --log_dir ./logs/classifier \
    --batch_size 32 \
    --iterations 100000

# 3. 使用 CG 生成样本
python scripts/classifier_sample.py \
    --model_path ./logs/diffusion_model/model010000.pt \
    --classifier_path ./logs/classifier/classifier_final.pt \
    --num_classes 10 \
    --classifier_scale 1.0 \
    --num_samples 16 \
    --output_dir ./samples
```

### 引导强度效果
- `guidance_scale = 1.0`: 标准条件生成
- `guidance_scale = 2.0-3.0`: 适度引导，平衡质量和多样性
- `guidance_scale = 5.0-10.0`: 强引导，高质量但多样性降低

## 🔧 技术实现细节

### CG 分类器实现
```python
# 分类器网络结构
class Classifier(nn.Module):
    def __init__(self, image_size, num_classes, model_channels, ...):
        # 时间嵌入
        self.time_embed = nn.Sequential(...)
        # 下采样块
        self.down_blocks = nn.ModuleList([...])
        # 输出层（注意力池化或空间池化）
        self.out = AttentionPool2d(...)

    def forward(self, x, timesteps):
        emb = self.time_embed(timestep_embedding(timesteps, ...))
        # 处理图像和时间嵌入
        return logits
```

### CG 引导函数
```python
def cond_fn(x, t, y, classifier, classifier_scale):
    with torch.enable_grad():
        x_in = x.detach().requires_grad_(True)
        logits = classifier(x_in, t)
        log_probs = F.log_softmax(logits, dim=-1)
        selected = log_probs[range(len(logits)), y.view(-1)]
        gradient = torch.autograd.grad(selected.sum(), x_in)[0]
        return gradient * classifier_scale
```

### CG 采样实现
```python
# 在 p_mean_variance 中应用分类器梯度
if cond_fn is not None:
    gradient = cond_fn(x, self._scale_timesteps(t), **model_kwargs)
    new_mean = model_mean.float() + model_variance * gradient.float()
    model_mean = new_mean
```

## 📁 新增文件列表

```
diffusion_v2/
├── classifier.py                    # 分类器模型定义
├── scripts/
│   ├── train_class_conditional.py  # 类别条件扩散模型训练
│   ├── classifier_train.py         # 分类器训练脚本
│   └── classifier_sample.py        # CG 采样脚本
├── test_cg_simple.py               # CG 功能测试
├── example_usage.py                 # 使用示例脚本
├── README.md                        # 详细使用说明
└── PROJECT_SUMMARY.md               # 本文件
```

## 🎯 核心优势

1. **向后兼容**: 所有修改都保持与原版的兼容性
2. **模块化设计**: 新功能独立实现，不影响原有逻辑
3. **易于使用**: 提供专用脚本和详细文档
4. **功能完整**: 从训练到采样的完整 CFG 流程
5. **灵活配置**: 丰富的参数选项满足不同需求

## 🔮 使用建议

### 训练建议
- 使用 `cfg_dropout_prob = 0.1` 作为起始值
- 根据数据集大小调整 `batch_size`
- 定期保存检查点 (`save_interval = 1000`)

### 采样建议
- 从 `guidance_scale = 2.0` 开始尝试
- 对比不同引导强度的效果
- 使用 EMA 权重获得更好的生成质量

### 数据集建议
- 确保 ImageFolder 格式正确
- 类别名称将按字母顺序排序
- 每个类别至少包含足够的训练样本

## 🎉 项目成果

成功实现了两个核心功能：
1. ✅ **自定义权重保存路径** - 提供 `--log_dir` 参数
2. ✅ **CG 类别引导训练** - 完整的 Classifier Guidance 训练和采样流程

项目为扩散模型的类别条件生成提供了完整的解决方案，支持从数据准备到模型训练再到样本生成的全流程。与 CFG 不同，CG 使用独立训练的分类器来引导生成过程，提供了更灵活的控制方式。
