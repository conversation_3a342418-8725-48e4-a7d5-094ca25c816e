# Diffusion V2 项目总结

## 🎯 项目目标

基于原有的 `improved_diffusion` 代码，实现两个关键功能：
1. **可指定权重保存路径** - 在训练脚本中指定自定义的日志和权重保存目录
2. **类别引导训练 (CFG)** - 使用 ImageFolder 格式数据集进行 Classifier-Free Guidance 训练

## ✅ 完成的功能

### 1. 自定义权重保存路径 ✅
- **修改文件**: `train_util.py`, `scripts/image_train.py`
- **新增参数**: `--log_dir` 
- **功能**: 
  - 支持指定自定义目录保存模型权重和训练日志
  - 自动创建目录结构
  - 兼容原有的默认行为

**使用方法**:
```bash
python scripts/image_train.py --log_dir ./my_experiment_logs
```

### 2. ImageFolder 数据集支持 ✅
- **修改文件**: `image_datasets.py`
- **新增参数**: `--use_imagefolder`
- **功能**:
  - 自动从目录结构提取类别标签
  - 支持标准 ImageFolder 格式
  - 自动生成类别到索引的映射

**数据集格式**:
```
dataset/
├── class1/
│   ├── image1.jpg
│   └── image2.jpg
├── class2/
│   └── image1.jpg
└── class3/
    └── image1.jpg
```

### 3. Classifier-Free Guidance (CFG) 训练 ✅
- **修改文件**: `gaussian_diffusion.py`, `unet.py`, `train_util.py`
- **新增参数**: `--cfg_dropout_prob`
- **功能**:
  - 训练时随机丢弃类别标签
  - UNet 支持无条件标签 (-1)
  - 为 CFG 采样做准备

**CFG 原理**:
- 训练时：以概率 `cfg_dropout_prob` 将类别标签设为 -1
- 采样时：结合条件和无条件预测进行引导

### 4. CFG 采样脚本 ✅
- **新增文件**: `scripts/image_sample_cfg.py`
- **功能**:
  - 支持 Classifier-Free Guidance 采样
  - 可调节引导强度 (`guidance_scale`)
  - 支持指定类别或随机类别生成

**使用方法**:
```bash
python scripts/image_sample_cfg.py \
    --model_path model.pt \
    --guidance_scale 2.0 \
    --num_samples 16
```

### 5. 专用训练脚本 ✅
- **新增文件**: `scripts/train_class_conditional.py`
- **功能**:
  - 专门用于类别条件训练
  - 预配置 CFG 相关参数
  - 简化的参数设置

## 🧪 测试结果

运行 `test_simple.py` 的测试结果：
- ✅ **CFG 训练逻辑**: 标签丢弃机制正常工作
- ✅ **UNet CFG 处理**: 正确处理无条件标签 (-1)
- ✅ **扩散过程**: 前向扩散过程正常
- ✅ **ImageFolder 逻辑**: 类别提取逻辑正确
- ⚠️ **MPI 导入**: 由于 Windows 环境限制，MPI 相关功能无法测试

## 📋 新增参数说明

### 训练参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--log_dir` | `""` | 自定义日志和权重保存目录 |
| `--use_imagefolder` | `True` | 使用 ImageFolder 格式数据集 |
| `--cfg_dropout_prob` | `0.1` | CFG 训练时标签丢弃概率 |

### 采样参数
| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--guidance_scale` | `1.0` | CFG 引导强度 (1.0=无引导) |
| `--specific_class` | `-1` | 指定生成类别 (-1=随机) |
| `--output_dir` | `./samples` | 生成样本保存目录 |

## 🚀 使用示例

### 完整训练流程
```bash
# 1. 训练类别条件模型
python scripts/train_class_conditional.py \
    --data_dir /path/to/imagefolder/dataset \
    --num_classes 10 \
    --log_dir ./logs/experiment1 \
    --cfg_dropout_prob 0.1 \
    --batch_size 16 \
    --save_interval 1000

# 2. 使用 CFG 生成样本
python scripts/image_sample_cfg.py \
    --model_path ./logs/experiment1/model010000.pt \
    --num_classes 10 \
    --guidance_scale 2.0 \
    --num_samples 16 \
    --output_dir ./samples
```

### 引导强度效果
- `guidance_scale = 1.0`: 标准条件生成
- `guidance_scale = 2.0-3.0`: 适度引导，平衡质量和多样性
- `guidance_scale = 5.0-10.0`: 强引导，高质量但多样性降低

## 🔧 技术实现细节

### CFG 训练实现
```python
# 在 training_losses 中随机丢弃标签
if "y" in model_kwargs and cfg_dropout_prob > 0:
    drop_mask = torch.rand(batch_size) < cfg_dropout_prob
    y_uncond = torch.full_like(model_kwargs["y"], -1)
    model_kwargs["y"] = torch.where(drop_mask, y_uncond, model_kwargs["y"])
```

### UNet 无条件处理
```python
# 处理 -1 标签（无条件）
valid_mask = y >= 0
y_safe = torch.where(valid_mask, y, torch.zeros_like(y))
label_emb = self.label_emb(y_safe)
label_emb = label_emb * valid_mask.float().unsqueeze(1)
```

### CFG 采样实现
```python
# 结合条件和无条件预测
cond_pred = model(x, t, y=class_labels)
uncond_pred = model(x, t, y=torch.full_like(class_labels, -1))
guided_pred = uncond_pred + guidance_scale * (cond_pred - uncond_pred)
```

## 📁 新增文件列表

```
diffusion_v2/
├── scripts/
│   ├── train_class_conditional.py    # 类别条件训练脚本
│   └── image_sample_cfg.py          # CFG 采样脚本
├── test_simple.py                   # 核心功能测试
├── example_usage.py                 # 使用示例脚本
├── README.md                        # 详细使用说明
└── PROJECT_SUMMARY.md               # 本文件
```

## 🎯 核心优势

1. **向后兼容**: 所有修改都保持与原版的兼容性
2. **模块化设计**: 新功能独立实现，不影响原有逻辑
3. **易于使用**: 提供专用脚本和详细文档
4. **功能完整**: 从训练到采样的完整 CFG 流程
5. **灵活配置**: 丰富的参数选项满足不同需求

## 🔮 使用建议

### 训练建议
- 使用 `cfg_dropout_prob = 0.1` 作为起始值
- 根据数据集大小调整 `batch_size`
- 定期保存检查点 (`save_interval = 1000`)

### 采样建议
- 从 `guidance_scale = 2.0` 开始尝试
- 对比不同引导强度的效果
- 使用 EMA 权重获得更好的生成质量

### 数据集建议
- 确保 ImageFolder 格式正确
- 类别名称将按字母顺序排序
- 每个类别至少包含足够的训练样本

## 🎉 项目成果

成功实现了两个核心功能：
1. ✅ **自定义权重保存路径** - 提供 `--log_dir` 参数
2. ✅ **CFG 类别引导训练** - 完整的 CFG 训练和采样流程

项目为扩散模型的类别条件生成提供了完整的解决方案，支持从数据准备到模型训练再到样本生成的全流程。
