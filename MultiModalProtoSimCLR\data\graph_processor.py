"""
图数据处理器 - 处理从PCAP提取的图数据用于GCN
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional
import networkx as nx
from sklearn.preprocessing import StandardScaler
import pickle


class GraphProcessor:
    """
    图数据处理器，用于处理网络流量图数据
    """
    
    def __init__(self, config: Dict):
        """
        初始化图处理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.max_nodes = config.get('graph_max_nodes', 100)
        self.node_feature_dim = config.get('node_feature_dim', 64)
        self.edge_feature_dim = config.get('edge_feature_dim', 2)
        
        # 特征缩放器
        self.node_scaler = StandardScaler()
        self.edge_scaler = StandardScaler()
        self.fitted = False
    
    def process_graph(self, graph_data: Dict) -> Dict:
        """
        处理图数据
        
        Args:
            graph_data: 从PCAP提取的图数据
            
        Returns:
            处理后的图数据
        """
        adj_matrix = graph_data['adj_matrix']
        node_features = graph_data['node_features']
        edge_index = graph_data['edge_index']
        edge_features = graph_data['edge_features']
        
        # 标准化节点特征
        if not self.fitted:
            # 只对非零特征进行拟合
            non_zero_mask = np.any(node_features != 0, axis=1)
            if np.any(non_zero_mask):
                self.node_scaler.fit(node_features[non_zero_mask])
                if len(edge_features) > 0:
                    self.edge_scaler.fit(edge_features)
                self.fitted = True
        
        if self.fitted:
            # 标准化节点特征
            normalized_node_features = np.zeros_like(node_features)
            non_zero_mask = np.any(node_features != 0, axis=1)
            if np.any(non_zero_mask):
                normalized_node_features[non_zero_mask] = self.node_scaler.transform(node_features[non_zero_mask])
            
            # 标准化边特征
            normalized_edge_features = edge_features.copy()
            if len(edge_features) > 0 and hasattr(self.edge_scaler, 'scale_'):
                normalized_edge_features = self.edge_scaler.transform(edge_features)
        else:
            normalized_node_features = node_features
            normalized_edge_features = edge_features
        
        # 计算图的全局特征
        global_features = self._compute_global_features(adj_matrix, normalized_node_features)
        
        # 创建PyTorch Geometric格式的数据
        processed_data = {
            'x': torch.tensor(normalized_node_features, dtype=torch.float32),
            'edge_index': torch.tensor(edge_index, dtype=torch.long),
            'edge_attr': torch.tensor(normalized_edge_features, dtype=torch.float32),
            'adj_matrix': torch.tensor(adj_matrix, dtype=torch.float32),
            'global_features': torch.tensor(global_features, dtype=torch.float32),
            'num_nodes': min(graph_data['num_nodes'], self.max_nodes),
            'num_edges': graph_data['num_edges'],
            'batch': torch.zeros(min(graph_data['num_nodes'], self.max_nodes), dtype=torch.long)
        }
        
        return processed_data
    
    def _compute_global_features(self, adj_matrix: np.ndarray, node_features: np.ndarray) -> np.ndarray:
        """
        计算图的全局特征
        
        Args:
            adj_matrix: 邻接矩阵
            node_features: 节点特征矩阵
            
        Returns:
            全局特征向量
        """
        # 基本图统计
        num_nodes = adj_matrix.shape[0]
        num_edges = np.sum(adj_matrix > 0) // 2  # 无向图
        density = num_edges / (num_nodes * (num_nodes - 1) / 2) if num_nodes > 1 else 0
        
        # 度分布统计
        degrees = np.sum(adj_matrix > 0, axis=1)
        avg_degree = np.mean(degrees) if len(degrees) > 0 else 0
        max_degree = np.max(degrees) if len(degrees) > 0 else 0
        degree_std = np.std(degrees) if len(degrees) > 0 else 0
        
        # 节点特征统计
        node_feature_mean = np.mean(node_features, axis=0) if len(node_features) > 0 else np.zeros(node_features.shape[1])
        node_feature_std = np.std(node_features, axis=0) if len(node_features) > 0 else np.zeros(node_features.shape[1])
        
        # 连通性特征
        try:
            G = nx.from_numpy_array(adj_matrix)
            num_components = nx.number_connected_components(G)
            largest_cc_size = len(max(nx.connected_components(G), key=len)) if num_components > 0 else 0
            avg_clustering = nx.average_clustering(G) if num_nodes > 0 else 0
        except:
            num_components = 1
            largest_cc_size = num_nodes
            avg_clustering = 0
        
        # 组合全局特征
        global_features = np.concatenate([
            [num_nodes, num_edges, density, avg_degree, max_degree, degree_std],
            [num_components, largest_cc_size, avg_clustering],
            node_feature_mean[:3] if len(node_feature_mean) >= 3 else np.zeros(3),  # 取前3个特征的均值
            node_feature_std[:3] if len(node_feature_std) >= 3 else np.zeros(3)     # 取前3个特征的标准差
        ])
        
        return global_features.astype(np.float32)
    
    def create_subgraphs(self, graph_data: Dict, subgraph_size: int = 20) -> List[Dict]:
        """
        创建子图用于数据增强
        
        Args:
            graph_data: 原始图数据
            subgraph_size: 子图大小
            
        Returns:
            子图列表
        """
        adj_matrix = graph_data['adj_matrix']
        node_features = graph_data['node_features']
        
        G = nx.from_numpy_array(adj_matrix)
        subgraphs = []
        
        # 随机游走采样子图
        for _ in range(3):  # 生成3个子图
            if len(G.nodes()) <= subgraph_size:
                subgraphs.append(graph_data)
                continue
            
            # 随机选择起始节点
            start_node = np.random.choice(list(G.nodes()))
            
            # 执行随机游走
            subgraph_nodes = self._random_walk_sampling(G, start_node, subgraph_size)
            
            # 提取子图
            subgraph = G.subgraph(subgraph_nodes).copy()
            
            # 重新映射节点索引
            node_mapping = {old_node: new_node for new_node, old_node in enumerate(subgraph.nodes())}
            
            # 构建子图数据
            sub_adj = nx.adjacency_matrix(subgraph, nodelist=list(node_mapping.keys())).toarray()
            sub_node_features = node_features[list(node_mapping.keys())]
            
            # 构建边索引
            sub_edge_index = []
            sub_edge_features = []
            for src, dst, data in subgraph.edges(data=True):
                src_idx = node_mapping[src]
                dst_idx = node_mapping[dst]
                weight = data.get('weight', 1.0)
                
                sub_edge_index.extend([[src_idx, dst_idx], [dst_idx, src_idx]])
                sub_edge_features.extend([[weight, 1.0], [weight, 1.0]])
            
            if not sub_edge_index:
                sub_edge_index = [[0, 0]]
                sub_edge_features = [[0.0, 0.0]]
            
            # 填充到固定大小
            if len(sub_node_features) < self.max_nodes:
                padding_size = self.max_nodes - len(sub_node_features)
                padding = np.zeros((padding_size, sub_node_features.shape[1]))
                sub_node_features = np.vstack([sub_node_features, padding])
                
                # 扩展邻接矩阵
                padded_adj = np.zeros((self.max_nodes, self.max_nodes))
                padded_adj[:sub_adj.shape[0], :sub_adj.shape[1]] = sub_adj
                sub_adj = padded_adj
            
            subgraph_data = {
                'adj_matrix': sub_adj.astype(np.float32),
                'node_features': sub_node_features.astype(np.float32),
                'edge_index': np.array(sub_edge_index).T.astype(np.int64),
                'edge_features': np.array(sub_edge_features).astype(np.float32),
                'num_nodes': len(subgraph.nodes()),
                'num_edges': len(subgraph.edges()),
                'node_mapping': node_mapping
            }
            
            subgraphs.append(subgraph_data)
        
        return subgraphs
    
    def _random_walk_sampling(self, G: nx.Graph, start_node: int, target_size: int) -> List[int]:
        """
        随机游走采样节点
        
        Args:
            G: NetworkX图
            start_node: 起始节点
            target_size: 目标节点数
            
        Returns:
            采样的节点列表
        """
        visited = set([start_node])
        current_node = start_node
        
        while len(visited) < target_size and len(visited) < len(G.nodes()):
            neighbors = list(G.neighbors(current_node))
            if not neighbors:
                # 如果没有邻居，随机选择一个未访问的节点
                unvisited = set(G.nodes()) - visited
                if unvisited:
                    current_node = np.random.choice(list(unvisited))
                    visited.add(current_node)
                else:
                    break
            else:
                # 随机选择一个邻居
                next_node = np.random.choice(neighbors)
                visited.add(next_node)
                current_node = next_node
        
        return list(visited)
    
    def apply_graph_augmentation(self, graph_data: Dict) -> List[Dict]:
        """
        应用图数据增强
        
        Args:
            graph_data: 原始图数据
            
        Returns:
            增强后的图数据列表
        """
        augmented_graphs = []
        
        # 原始图
        augmented_graphs.append(graph_data)
        
        # 节点丢弃
        node_drop_graph = self._node_dropout(graph_data.copy(), drop_prob=0.1)
        augmented_graphs.append(node_drop_graph)
        
        # 边丢弃
        edge_drop_graph = self._edge_dropout(graph_data.copy(), drop_prob=0.1)
        augmented_graphs.append(edge_drop_graph)
        
        # 特征噪声
        feature_noise_graph = self._add_feature_noise(graph_data.copy(), noise_std=0.1)
        augmented_graphs.append(feature_noise_graph)
        
        return augmented_graphs
    
    def _node_dropout(self, graph_data: Dict, drop_prob: float = 0.1) -> Dict:
        """
        节点丢弃增强
        
        Args:
            graph_data: 图数据
            drop_prob: 丢弃概率
            
        Returns:
            处理后的图数据
        """
        adj_matrix = graph_data['adj_matrix'].copy()
        node_features = graph_data['node_features'].copy()
        
        num_nodes = graph_data['num_nodes']
        num_drop = int(num_nodes * drop_prob)
        
        if num_drop > 0:
            # 随机选择要丢弃的节点
            drop_indices = np.random.choice(num_nodes, num_drop, replace=False)
            
            # 将节点特征置零
            node_features[drop_indices] = 0
            
            # 移除相关的边
            adj_matrix[drop_indices, :] = 0
            adj_matrix[:, drop_indices] = 0
        
        graph_data['adj_matrix'] = adj_matrix
        graph_data['node_features'] = node_features
        
        return graph_data
    
    def _edge_dropout(self, graph_data: Dict, drop_prob: float = 0.1) -> Dict:
        """
        边丢弃增强
        
        Args:
            graph_data: 图数据
            drop_prob: 丢弃概率
            
        Returns:
            处理后的图数据
        """
        adj_matrix = graph_data['adj_matrix'].copy()
        
        # 获取所有边的位置
        edge_positions = np.where(adj_matrix > 0)
        num_edges = len(edge_positions[0])
        num_drop = int(num_edges * drop_prob)
        
        if num_drop > 0:
            # 随机选择要丢弃的边
            drop_indices = np.random.choice(num_edges, num_drop, replace=False)
            
            # 移除边
            for idx in drop_indices:
                i, j = edge_positions[0][idx], edge_positions[1][idx]
                adj_matrix[i, j] = 0
                adj_matrix[j, i] = 0  # 无向图
        
        graph_data['adj_matrix'] = adj_matrix
        
        return graph_data
    
    def _add_feature_noise(self, graph_data: Dict, noise_std: float = 0.1) -> Dict:
        """
        添加特征噪声
        
        Args:
            graph_data: 图数据
            noise_std: 噪声标准差
            
        Returns:
            处理后的图数据
        """
        node_features = graph_data['node_features'].copy()
        
        # 只对非零特征添加噪声
        non_zero_mask = node_features != 0
        noise = np.random.normal(0, noise_std, node_features.shape)
        node_features[non_zero_mask] += noise[non_zero_mask]
        
        graph_data['node_features'] = node_features
        
        return graph_data
    
    def save_processor(self, save_path: str):
        """
        保存处理器状态
        
        Args:
            save_path: 保存路径
        """
        state = {
            'config': self.config,
            'node_scaler': self.node_scaler,
            'edge_scaler': self.edge_scaler,
            'fitted': self.fitted
        }
        
        with open(save_path, 'wb') as f:
            pickle.dump(state, f)
    
    def load_processor(self, load_path: str):
        """
        加载处理器状态
        
        Args:
            load_path: 加载路径
        """
        with open(load_path, 'rb') as f:
            state = pickle.load(f)
        
        self.config = state['config']
        self.node_scaler = state['node_scaler']
        self.edge_scaler = state['edge_scaler']
        self.fitted = state['fitted']


if __name__ == "__main__":
    # 测试代码
    config = {
        'graph_max_nodes': 100,
        'node_feature_dim': 64,
        'edge_feature_dim': 2
    }
    
    processor = GraphProcessor(config)
    
    # 测试图处理
    test_data = {
        'adj_matrix': np.random.randint(0, 2, (50, 50)),
        'node_features': np.random.randn(50, 6),
        'edge_index': np.random.randint(0, 50, (2, 100)),
        'edge_features': np.random.randn(100, 2),
        'num_nodes': 50,
        'num_edges': 100
    }
    
    result = processor.process_graph(test_data)
    print("Processed graph node features shape:", result['x'].shape)
    print("Edge index shape:", result['edge_index'].shape)
    print("Global features shape:", result['global_features'].shape)
