"""
Generate image samples using Classifier Guidance (CG).
This script uses a separately trained classifier to guide the generation process.
"""

import argparse
import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import numpy as np
import torch as th
import torch.nn.functional as F
from PIL import Image

from diffusion_v2 import dist_util, logger
from diffusion_v2.script_util import (
    model_and_diffusion_defaults,
    classifier_defaults,
    create_model_and_diffusion,
    create_classifier,
    add_dict_to_argparser,
    args_to_dict,
)


def cond_fn(x, t, y, classifier, classifier_scale):
    """
    Compute the gradient of the classifier for Classifier Guidance.
    
    Args:
        x: Current noisy image
        t: Current timestep
        y: Target class labels
        classifier: The trained classifier
        classifier_scale: Guidance scale
        
    Returns:
        Gradient for guidance
    """
    assert y is not None
    with th.enable_grad():
        x_in = x.detach().requires_grad_(True)
        logits = classifier(x_in, t)
        log_probs = F.log_softmax(logits, dim=-1)
        selected = log_probs[range(len(logits)), y.view(-1)]
        gradient = th.autograd.grad(selected.sum(), x_in)[0]
        return gradient * classifier_scale


def main():
    args = create_argparser().parse_args()

    dist_util.setup_dist()
    logger.configure()

    logger.log("creating model and diffusion...")
    model, diffusion = create_model_and_diffusion(
        **args_to_dict(args, model_and_diffusion_defaults().keys())
    )
    model.load_state_dict(
        dist_util.load_state_dict(args.model_path, map_location="cpu")
    )
    model.to(dist_util.dev())
    model.eval()

    logger.log("loading classifier...")
    classifier = create_classifier(**args_to_dict(args, classifier_defaults().keys()))
    classifier.load_state_dict(
        dist_util.load_state_dict(args.classifier_path, map_location="cpu")
    )
    classifier.to(dist_util.dev())
    classifier.eval()

    logger.log("sampling...")
    all_images = []
    all_labels = []
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    sample_count = 0
    while sample_count < args.num_samples:
        batch_size = min(args.batch_size, args.num_samples - sample_count)
        
        # Prepare class labels
        if args.specific_class >= 0:
            # Generate specific class
            class_labels = th.full((batch_size,), args.specific_class, dtype=th.long)
        else:
            # Random classes
            class_labels = th.randint(
                low=0, high=args.num_classes, size=(batch_size,), dtype=th.long
            )
        
        class_labels = class_labels.to(dist_util.dev())
        
        logger.log(f"Generating batch {sample_count // args.batch_size + 1}...")
        logger.log(f"Target classes: {class_labels.cpu().numpy()}")
        
        # Create conditioning function
        def model_cond_fn(x, t, **kwargs):
            return cond_fn(x, t, class_labels, classifier, args.classifier_scale)
        
        # Generate samples with Classifier Guidance
        model_kwargs = {"y": class_labels} if args.class_cond else {}
        
        samples = diffusion.p_sample_loop(
            model,
            (batch_size, 3, args.image_size, args.image_size),
            clip_denoised=args.clip_denoised,
            model_kwargs=model_kwargs,
            cond_fn=model_cond_fn,
            device=dist_util.dev(),
            progress=True,
        )
        
        # Convert to numpy and store
        samples = ((samples + 1) * 127.5).clamp(0, 255).to(th.uint8)
        samples = samples.permute(0, 2, 3, 1).cpu().numpy()
        
        all_images.append(samples)
        all_labels.append(class_labels.cpu().numpy())
        
        # Save individual images
        for i, sample in enumerate(samples):
            img = Image.fromarray(sample)
            class_id = class_labels[i].item()
            filename = f"sample_{sample_count + i:06d}_class_{class_id}.png"
            img.save(os.path.join(args.output_dir, filename))
        
        sample_count += batch_size
        logger.log(f"Generated {sample_count}/{args.num_samples} samples")

    # Save all samples as numpy arrays
    all_images = np.concatenate(all_images, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)
    
    np.savez(
        os.path.join(args.output_dir, "samples.npz"),
        images=all_images,
        labels=all_labels,
    )
    
    logger.log(f"Sampling complete. Saved {len(all_images)} samples to {args.output_dir}")


def create_argparser():
    defaults = dict(
        # Model and classifier paths
        model_path="",
        classifier_path="",
        
        # Sampling settings
        clip_denoised=True,
        num_samples=16,
        batch_size=4,
        classifier_scale=1.0,  # Classifier guidance scale
        specific_class=-1,     # Generate specific class (-1 for random)
        output_dir="./samples",
        
        # Use DDIM sampling
        use_ddim=False,
    )
    
    # Add model and diffusion defaults
    defaults.update(model_and_diffusion_defaults())
    
    # Add classifier defaults
    defaults.update(classifier_defaults())
    
    parser = argparse.ArgumentParser()
    add_dict_to_argparser(parser, defaults)
    
    # Add helpful descriptions
    parser.add_argument(
        "--model_path", 
        help="Path to the trained diffusion model"
    )
    parser.add_argument(
        "--classifier_path", 
        help="Path to the trained classifier model"
    )
    parser.add_argument(
        "--classifier_scale", 
        type=float,
        help="Classifier guidance scale (higher = stronger guidance)"
    )
    parser.add_argument(
        "--specific_class", 
        type=int,
        help="Generate specific class (-1 for random classes)"
    )
    parser.add_argument(
        "--output_dir", 
        help="Directory to save generated samples"
    )
    
    return parser


if __name__ == "__main__":
    main()
