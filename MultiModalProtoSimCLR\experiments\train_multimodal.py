"""
多模态训练主脚本
"""

import os
import sys
import argparse
import yaml
import torch
import random
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from models.multimodal_protonet import MultiModalProtoNet
from training.multimodal_trainer import MultiModalTrainer
from data.multimodal_dataset import MultiModalFewShotDataset, MultiModalContrastiveDataset


def set_seed(seed: int):
    """
    设置随机种子
    
    Args:
        seed: 随机种子
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"✅ Random seed set to {seed}")


def load_config(config_path: str) -> dict:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    print(f"✅ Config loaded from {config_path}")
    return config


def create_datasets(config: dict):
    """
    创建数据集
    
    Args:
        config: 配置字典
        
    Returns:
        (对比学习训练集, 对比学习验证集, 元学习训练集, 元学习验证集)
    """
    data_root = config['data']['train_dataset']
    
    # 对比学习数据集
    contrastive_train_dataset = MultiModalContrastiveDataset(
        config=config,
        data_root=data_root,
        split='train'
    )
    
    contrastive_val_dataset = MultiModalContrastiveDataset(
        config=config,
        data_root=config['data']['val_dataset'],
        split='val'
    )
    
    # 元学习数据集
    meta_train_dataset = MultiModalFewShotDataset(
        config=config,
        data_root=data_root,
        split='train'
    )
    
    meta_val_dataset = MultiModalFewShotDataset(
        config=config,
        data_root=config['data']['val_dataset'],
        split='val'
    )
    
    print(f"✅ Datasets created")
    print(f"   Contrastive train: {len(contrastive_train_dataset)} samples")
    print(f"   Contrastive val: {len(contrastive_val_dataset)} samples")
    print(f"   Meta train: {len(meta_train_dataset)} episodes")
    print(f"   Meta val: {len(meta_val_dataset)} episodes")
    
    return contrastive_train_dataset, contrastive_val_dataset, meta_train_dataset, meta_val_dataset


def main():
    """
    主训练函数
    """
    parser = argparse.ArgumentParser(description='Multi-Modal ProtoSimCLR Training')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--resume', type=str, default=None, help='Path to checkpoint to resume from')
    parser.add_argument('--stage', type=str, choices=['stage1', 'stage2', 'both'], default='both',
                       help='Training stage to run')
    parser.add_argument('--gpu', type=int, default=0, help='GPU device ID')
    parser.add_argument('--seed', type=int, default=None, help='Random seed (overrides config)')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 设置设备
    if torch.cuda.is_available():
        torch.cuda.set_device(args.gpu)
        config['training']['device'] = f'cuda:{args.gpu}'
        print(f"✅ Using GPU: {args.gpu}")
    else:
        config['training']['device'] = 'cpu'
        print("✅ Using CPU")
    
    # 设置随机种子
    seed = args.seed if args.seed is not None else config['training']['seed']
    set_seed(seed)
    
    # 创建模型
    print("\n🚀 Creating model...")
    model = MultiModalProtoNet(config)
    
    # 打印模型信息
    param_count = model.get_parameter_count()
    print(f"   Total parameters: {param_count['total_parameters']:,}")
    print(f"   Trainable parameters: {param_count['total_trainable']:,}")
    
    # 创建训练器
    print("\n🚀 Creating trainer...")
    trainer = MultiModalTrainer(config, model)
    
    # 如果需要恢复训练
    start_epoch = 0
    if args.resume:
        start_epoch = trainer.load_checkpoint(args.resume)
    
    try:
        # 创建数据集
        print("\n🚀 Creating datasets...")
        contrastive_train, contrastive_val, meta_train, meta_val = create_datasets(config)
        
        # 阶段1：对比学习预训练
        if args.stage in ['stage1', 'both']:
            print("\n" + "="*50)
            print("STAGE 1: CONTRASTIVE PRE-TRAINING")
            print("="*50)
            
            trainer.stage1_contrastive_pretraining(
                train_dataset=contrastive_train,
                val_dataset=contrastive_val
            )
        
        # 阶段2：元学习微调
        if args.stage in ['stage2', 'both']:
            print("\n" + "="*50)
            print("STAGE 2: META-LEARNING FINE-TUNING")
            print("="*50)
            
            # 如果只运行stage2，需要加载stage1的最佳模型
            if args.stage == 'stage2' and not args.resume:
                stage1_checkpoint = os.path.join(config['logging']['save_dir'], 'stage1_best.pth')
                if os.path.exists(stage1_checkpoint):
                    trainer.load_checkpoint(stage1_checkpoint)
                    print(f"✅ Loaded Stage 1 checkpoint: {stage1_checkpoint}")
                else:
                    print("⚠️ No Stage 1 checkpoint found, starting from scratch")
            
            trainer.stage2_meta_learning(
                train_dataset=meta_train,
                val_dataset=meta_val
            )
        
        print("\n🎉 Training completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Training interrupted by user")
    except Exception as e:
        print(f"\n❌ Training failed with error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        trainer.close()


def test_training_setup():
    """
    测试训练设置（不实际训练）
    """
    print("🧪 Testing training setup...")
    
    # 使用测试配置
    config_path = "../config/multimodal_config.yaml"
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return
    
    config = load_config(config_path)
    
    # 修改为测试设置
    config['training']['stage1_epochs'] = 2
    config['training']['stage2_epochs'] = 2
    config['protonet']['episodes_num'] = 10
    config['training']['device'] = 'cpu'
    
    # 设置随机种子
    set_seed(42)
    
    # 创建模型
    model = MultiModalProtoNet(config)
    print(f"✅ Model created")
    
    # 创建训练器
    trainer = MultiModalTrainer(config, model)
    print(f"✅ Trainer created")
    
    # 测试数据创建（使用虚拟数据）
    print("✅ Training setup test completed")
    
    trainer.close()


if __name__ == "__main__":
    # 检查是否为测试模式
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        test_training_setup()
    else:
        main()
