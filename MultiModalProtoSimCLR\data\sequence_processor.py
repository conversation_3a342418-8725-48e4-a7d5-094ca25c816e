"""
序列数据处理器 - 处理从PCAP提取的序列数据用于Transformer
"""

import numpy as np
import torch
from typing import Dict, List, Tuple, Optional
from sklearn.preprocessing import StandardScaler, MinMaxScaler
import pickle
import json


class SequenceProcessor:
    """
    序列数据处理器，用于处理网络流量序列数据
    """
    
    def __init__(self, config: Dict):
        """
        初始化序列处理器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.sequence_length = config.get('sequence_length', 512)
        self.vocab_size = config.get('vocab_size', 10000)
        self.embedding_dim = config.get('embedding_dim', 256)
        
        # 特征缩放器
        self.scaler = StandardScaler()
        self.fitted = False
        
        # 特殊token
        self.PAD_TOKEN = 0
        self.UNK_TOKEN = 1
        self.CLS_TOKEN = 2
        self.SEP_TOKEN = 3
        
        # 词汇表
        self.vocab = {}
        self.reverse_vocab = {}
        self._build_vocab()
    
    def _build_vocab(self):
        """
        构建词汇表，将网络特征映射到token
        """
        # 特殊token
        special_tokens = {
            '<PAD>': self.PAD_TOKEN,
            '<UNK>': self.UNK_TOKEN, 
            '<CLS>': self.CLS_TOKEN,
            '<SEP>': self.SEP_TOKEN
        }
        
        self.vocab.update(special_tokens)
        
        # 协议类型token
        protocols = ['TCP', 'UDP', 'ICMP', 'HTTP', 'HTTPS', 'DNS', 'FTP', 'SSH', 'TELNET', 'SMTP']
        for i, protocol in enumerate(protocols):
            self.vocab[f'PROTO_{protocol}'] = len(self.vocab)
        
        # 端口范围token
        port_ranges = [
            (0, 1023, 'WELL_KNOWN'),
            (1024, 49151, 'REGISTERED'), 
            (49152, 65535, 'DYNAMIC')
        ]
        for start, end, name in port_ranges:
            self.vocab[f'PORT_{name}'] = len(self.vocab)
        
        # 包大小范围token
        size_ranges = [
            (0, 64, 'TINY'),
            (65, 512, 'SMALL'),
            (513, 1500, 'MEDIUM'),
            (1501, 9000, 'LARGE'),
            (9001, float('inf'), 'JUMBO')
        ]
        for start, end, name in size_ranges:
            self.vocab[f'SIZE_{name}'] = len(self.vocab)
        
        # 构建反向词汇表
        self.reverse_vocab = {v: k for k, v in self.vocab.items()}
    
    def process_sequence(self, sequence_data: Dict) -> Dict:
        """
        处理序列数据
        
        Args:
            sequence_data: 从PCAP提取的序列数据
            
        Returns:
            处理后的序列数据
        """
        features = sequence_data['features']
        timestamps = sequence_data['timestamps']
        packet_sizes = sequence_data['packet_sizes']
        
        # 特征标准化
        if not self.fitted:
            self.scaler.fit(features)
            self.fitted = True
        
        normalized_features = self.scaler.transform(features)
        
        # 转换为token序列
        token_sequence = self._features_to_tokens(normalized_features, packet_sizes)
        
        # 添加特殊token
        token_sequence = [self.CLS_TOKEN] + token_sequence + [self.SEP_TOKEN]
        
        # 填充或截断
        token_sequence = self._pad_or_truncate(token_sequence, self.sequence_length, self.PAD_TOKEN)
        
        # 创建attention mask
        attention_mask = [1 if token != self.PAD_TOKEN else 0 for token in token_sequence]
        
        # 位置编码
        position_ids = list(range(len(token_sequence)))
        
        # 时间特征处理
        time_features = self._process_time_features(timestamps)
        
        return {
            'input_ids': np.array(token_sequence, dtype=np.int64),
            'attention_mask': np.array(attention_mask, dtype=np.int64),
            'position_ids': np.array(position_ids, dtype=np.int64),
            'time_features': time_features,
            'raw_features': normalized_features.astype(np.float32),
            'sequence_length': len([t for t in token_sequence if t != self.PAD_TOKEN])
        }
    
    def _features_to_tokens(self, features: np.ndarray, packet_sizes: np.ndarray) -> List[int]:
        """
        将特征向量转换为token序列
        
        Args:
            features: 标准化后的特征矩阵
            packet_sizes: 包大小数组
            
        Returns:
            token序列
        """
        tokens = []
        
        for i, (feature_vec, size) in enumerate(zip(features, packet_sizes)):
            if size == 0:  # 填充的包
                continue
                
            # 协议token
            protocol_id = int(feature_vec[1]) if len(feature_vec) > 1 else 0
            if protocol_id > 0 and protocol_id <= 10:
                protocol_names = ['TCP', 'UDP', 'ICMP', 'HTTP', 'HTTPS', 'DNS', 'FTP', 'SSH', 'TELNET', 'SMTP']
                if protocol_id <= len(protocol_names):
                    protocol_token = self.vocab.get(f'PROTO_{protocol_names[protocol_id-1]}', self.UNK_TOKEN)
                    tokens.append(protocol_token)
            
            # 端口token
            if len(feature_vec) > 4:
                src_port = int(feature_vec[4])
                dst_port = int(feature_vec[5])
                
                for port in [src_port, dst_port]:
                    if port > 0:
                        port_token = self._port_to_token(port)
                        tokens.append(port_token)
            
            # 包大小token
            size_token = self._size_to_token(size)
            tokens.append(size_token)
        
        return tokens
    
    def _port_to_token(self, port: int) -> int:
        """
        将端口号转换为token
        
        Args:
            port: 端口号
            
        Returns:
            端口token
        """
        if 0 <= port <= 1023:
            return self.vocab.get('PORT_WELL_KNOWN', self.UNK_TOKEN)
        elif 1024 <= port <= 49151:
            return self.vocab.get('PORT_REGISTERED', self.UNK_TOKEN)
        elif 49152 <= port <= 65535:
            return self.vocab.get('PORT_DYNAMIC', self.UNK_TOKEN)
        else:
            return self.UNK_TOKEN
    
    def _size_to_token(self, size: int) -> int:
        """
        将包大小转换为token
        
        Args:
            size: 包大小
            
        Returns:
            大小token
        """
        if 0 <= size <= 64:
            return self.vocab.get('SIZE_TINY', self.UNK_TOKEN)
        elif 65 <= size <= 512:
            return self.vocab.get('SIZE_SMALL', self.UNK_TOKEN)
        elif 513 <= size <= 1500:
            return self.vocab.get('SIZE_MEDIUM', self.UNK_TOKEN)
        elif 1501 <= size <= 9000:
            return self.vocab.get('SIZE_LARGE', self.UNK_TOKEN)
        else:
            return self.vocab.get('SIZE_JUMBO', self.UNK_TOKEN)
    
    def _process_time_features(self, timestamps: np.ndarray) -> np.ndarray:
        """
        处理时间特征
        
        Args:
            timestamps: 时间戳数组
            
        Returns:
            时间特征数组
        """
        if len(timestamps) == 0:
            return np.zeros((self.sequence_length, 3), dtype=np.float32)
        
        # 计算时间间隔
        time_diffs = np.diff(timestamps, prepend=timestamps[0])
        
        # 计算累积时间
        cumulative_time = timestamps - timestamps[0]
        
        # 时间特征：[时间间隔, 累积时间, 归一化时间戳]
        max_time = max(cumulative_time) if max(cumulative_time) > 0 else 1.0
        normalized_time = cumulative_time / max_time
        
        time_features = np.column_stack([
            time_diffs,
            cumulative_time, 
            normalized_time
        ])
        
        # 填充到固定长度
        if len(time_features) < self.sequence_length:
            padding = np.zeros((self.sequence_length - len(time_features), 3))
            time_features = np.vstack([time_features, padding])
        else:
            time_features = time_features[:self.sequence_length]
        
        return time_features.astype(np.float32)
    
    def _pad_or_truncate(self, data: List, target_length: int, pad_value) -> List:
        """
        填充或截断数据
        
        Args:
            data: 输入数据
            target_length: 目标长度
            pad_value: 填充值
            
        Returns:
            处理后的数据
        """
        if len(data) >= target_length:
            return data[:target_length]
        else:
            return data + [pad_value] * (target_length - len(data))
    
    def create_data_augmentation(self, sequence_data: Dict) -> List[Dict]:
        """
        创建数据增强版本
        
        Args:
            sequence_data: 原始序列数据
            
        Returns:
            增强后的序列数据列表
        """
        augmented_data = []
        
        # 原始数据
        augmented_data.append(sequence_data)
        
        # 随机掩码
        masked_data = self._random_mask(sequence_data.copy())
        augmented_data.append(masked_data)
        
        # 随机替换
        replaced_data = self._random_replace(sequence_data.copy())
        augmented_data.append(replaced_data)
        
        return augmented_data
    
    def _random_mask(self, data: Dict, mask_prob: float = 0.15) -> Dict:
        """
        随机掩码增强
        
        Args:
            data: 输入数据
            mask_prob: 掩码概率
            
        Returns:
            掩码后的数据
        """
        input_ids = data['input_ids'].copy()
        
        for i in range(len(input_ids)):
            if np.random.random() < mask_prob and input_ids[i] not in [self.PAD_TOKEN, self.CLS_TOKEN, self.SEP_TOKEN]:
                input_ids[i] = self.UNK_TOKEN
        
        data['input_ids'] = input_ids
        return data
    
    def _random_replace(self, data: Dict, replace_prob: float = 0.1) -> Dict:
        """
        随机替换增强
        
        Args:
            data: 输入数据
            replace_prob: 替换概率
            
        Returns:
            替换后的数据
        """
        input_ids = data['input_ids'].copy()
        
        for i in range(len(input_ids)):
            if np.random.random() < replace_prob and input_ids[i] not in [self.PAD_TOKEN, self.CLS_TOKEN, self.SEP_TOKEN]:
                # 随机选择一个token替换
                input_ids[i] = np.random.randint(4, len(self.vocab))
        
        data['input_ids'] = input_ids
        return data
    
    def save_processor(self, save_path: str):
        """
        保存处理器状态
        
        Args:
            save_path: 保存路径
        """
        state = {
            'config': self.config,
            'vocab': self.vocab,
            'reverse_vocab': self.reverse_vocab,
            'scaler': self.scaler,
            'fitted': self.fitted
        }
        
        with open(save_path, 'wb') as f:
            pickle.dump(state, f)
    
    def load_processor(self, load_path: str):
        """
        加载处理器状态
        
        Args:
            load_path: 加载路径
        """
        with open(load_path, 'rb') as f:
            state = pickle.load(f)
        
        self.config = state['config']
        self.vocab = state['vocab']
        self.reverse_vocab = state['reverse_vocab']
        self.scaler = state['scaler']
        self.fitted = state['fitted']


if __name__ == "__main__":
    # 测试代码
    config = {
        'sequence_length': 512,
        'vocab_size': 10000,
        'embedding_dim': 256
    }
    
    processor = SequenceProcessor(config)
    
    # 测试序列处理
    test_data = {
        'features': np.random.randn(100, 8),
        'timestamps': np.sort(np.random.random(100)),
        'packet_sizes': np.random.randint(64, 1500, 100),
        'length': 100
    }
    
    result = processor.process_sequence(test_data)
    print("Processed sequence shape:", result['input_ids'].shape)
    print("Attention mask shape:", result['attention_mask'].shape)
    print("Time features shape:", result['time_features'].shape)
