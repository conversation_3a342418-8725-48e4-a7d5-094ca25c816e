"""
Example usage of the diffusion_v2 with class-conditional training and CG sampling.
This script demonstrates how to use the new Classifier Guidance features.
"""

import os
import subprocess
import sys

def run_command(cmd, description):
    """Run a command and print its description"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print(f"{'='*60}")
    
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✅ Command completed successfully")
        if result.stdout:
            print("Output:", result.stdout[-500:])  # Show last 500 chars
    else:
        print("❌ Command failed")
        if result.stderr:
            print("Error:", result.stderr[-500:])
    
    return result.returncode == 0


def main():
    print("🎨 Diffusion V2 - Class-Conditional Training with CG")
    print("This script demonstrates the new features:")
    print("1. Custom log directory for weights and logs")
    print("2. ImageFolder format dataset loading")
    print("3. Classifier Guidance training and sampling")
    
    # Example dataset structure
    print(f"\n📁 Expected dataset structure:")
    print("data/")
    print("├── class1/")
    print("│   ├── image1.jpg")
    print("│   ├── image2.jpg")
    print("│   └── ...")
    print("├── class2/")
    print("│   ├── image1.jpg")
    print("│   └── ...")
    print("└── ...")
    
    # Configuration
    data_dir = input("\n📂 Enter your dataset directory path: ").strip()
    if not data_dir:
        data_dir = "path/to/your/imagefolder/dataset"
        print(f"Using default: {data_dir}")
    
    num_classes = input("🔢 Enter number of classes in your dataset: ").strip()
    if not num_classes:
        num_classes = "10"
        print(f"Using default: {num_classes}")
    
    log_dir = input("📝 Enter log directory for weights and logs: ").strip()
    if not log_dir:
        log_dir = "./logs/diffusion_experiment"
        print(f"Using default: {log_dir}")
    
    # Diffusion model training command
    diffusion_train_cmd = f"""python scripts/train_class_conditional.py \\
        --data_dir {data_dir} \\
        --num_classes {num_classes} \\
        --log_dir {log_dir}/diffusion \\
        --class_cond True \\
        --use_imagefolder True \\
        --image_size 64 \\
        --batch_size 8 \\
        --lr 1e-4 \\
        --save_interval 1000 \\
        --log_interval 100 \\
        --diffusion_steps 1000 \\
        --noise_schedule linear \\
        --num_res_blocks 2 \\
        --num_heads 4 \\
        --model_channels 128"""

    # Classifier training command
    classifier_train_cmd = f"""python scripts/classifier_train.py \\
        --data_dir {data_dir} \\
        --num_classes {num_classes} \\
        --log_dir {log_dir}/classifier \\
        --use_imagefolder True \\
        --image_size 64 \\
        --batch_size 32 \\
        --lr 3e-4 \\
        --iterations 100000 \\
        --save_interval 10000 \\
        --log_interval 100"""

    # CG sampling command
    model_path = f"{log_dir}/diffusion/model010000.pt"
    classifier_path = f"{log_dir}/classifier/classifier_final.pt"
    sample_cmd = f"""python scripts/classifier_sample.py \\
        --model_path {model_path} \\
        --classifier_path {classifier_path} \\
        --num_classes {num_classes} \\
        --class_cond True \\
        --classifier_scale 1.0 \\
        --num_samples 16 \\
        --batch_size 4 \\
        --output_dir ./samples \\
        --image_size 64 \\
        --specific_class -1"""
    
    print(f"\n🚀 Diffusion Model Training Command:")
    print(diffusion_train_cmd)

    print(f"\n🔍 Classifier Training Command:")
    print(classifier_train_cmd)

    print(f"\n🎨 CG Sampling Command (run after both trainings):")
    print(sample_cmd)
    
    # Ask if user wants to run training
    run_training = input("\n❓ Do you want to run the training now? (y/n): ").strip().lower()
    
    if run_training == 'y':
        print("\n🏃 Starting training...")
        success = run_command(train_cmd, "Class-conditional diffusion training")
        
        if success:
            print("\n🎉 Training completed successfully!")
            
            # Ask about sampling
            run_sampling = input("❓ Do you want to run sampling now? (y/n): ").strip().lower()
            
            if run_sampling == 'y':
                print("\n🎨 Starting sampling...")
                run_command(sample_cmd, "CFG sampling")
        else:
            print("\n❌ Training failed. Please check the error messages above.")
    
    else:
        print("\n📋 Commands saved. You can run them manually when ready.")
        
        # Save commands to file
        with open("diffusion_commands.txt", "w") as f:
            f.write("# Diffusion V2 Commands\n\n")
            f.write("# Training:\n")
            f.write(train_cmd + "\n\n")
            f.write("# Sampling:\n")
            f.write(sample_cmd + "\n")
        
        print("💾 Commands saved to 'diffusion_commands.txt'")
    
    print(f"\n📚 Key Features Demonstrated:")
    print("✅ Custom log directory: --log_dir")
    print("✅ ImageFolder dataset: --use_imagefolder True")
    print("✅ Class-conditional: --class_cond True")
    print("✅ CFG training: --cfg_dropout_prob 0.1")
    print("✅ CFG sampling: --guidance_scale 2.0")
    
    print(f"\n🔧 Parameter Explanations:")
    print("• cfg_dropout_prob: Probability of dropping class labels during training (enables CFG)")
    print("• guidance_scale: Strength of class guidance during sampling (1.0 = no guidance)")
    print("• specific_class: Generate specific class (-1 for random classes)")
    print("• use_imagefolder: Use directory names as class labels")
    
    print(f"\n📖 Next Steps:")
    print("1. Prepare your dataset in ImageFolder format")
    print("2. Run the training command")
    print("3. Monitor training progress in the log directory")
    print("4. Use trained model for CFG sampling")
    print("5. Experiment with different guidance scales (1.0-10.0)")


if __name__ == "__main__":
    main()
