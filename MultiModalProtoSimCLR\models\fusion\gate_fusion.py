"""
门控融合模块 - 使用门控机制动态调整各模态的贡献权重
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional


class ModalityGate(nn.Module):
    """
    单模态门控单元
    """
    
    def __init__(self, input_dim: int, hidden_dim: int, dropout: float = 0.1):
        """
        初始化模态门控单元
        
        Args:
            input_dim: 输入特征维度
            hidden_dim: 隐藏层维度
            dropout: Dropout率
        """
        super(ModalityGate, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        
        # 门控网络
        self.gate_network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 输出0-1之间的门控值
        )
        
        # 特征变换网络
        self.transform_network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, input_dim),
            nn.LayerNorm(input_dim)
        )
        
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化权重
        """
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, x: torch.Tensor) -> tuple:
        """
        前向传播
        
        Args:
            x: 输入特征 [batch_size, input_dim]
            
        Returns:
            (门控值, 变换后的特征)
        """
        # 计算门控值
        gate_value = self.gate_network(x)  # [batch_size, 1]
        
        # 特征变换
        transformed_features = self.transform_network(x)  # [batch_size, input_dim]
        
        # 应用门控
        gated_features = gate_value * transformed_features
        
        return gate_value, gated_features


class CrossModalGate(nn.Module):
    """
    跨模态门控单元
    """
    
    def __init__(self, modal1_dim: int, modal2_dim: int, hidden_dim: int, dropout: float = 0.1):
        """
        初始化跨模态门控单元
        
        Args:
            modal1_dim: 模态1特征维度
            modal2_dim: 模态2特征维度
            hidden_dim: 隐藏层维度
            dropout: Dropout率
        """
        super(CrossModalGate, self).__init__()
        
        self.modal1_dim = modal1_dim
        self.modal2_dim = modal2_dim
        self.hidden_dim = hidden_dim
        
        # 跨模态交互网络
        self.interaction_network = nn.Sequential(
            nn.Linear(modal1_dim + modal2_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 模态1的门控网络
        self.gate1_network = nn.Sequential(
            nn.Linear(hidden_dim // 2, modal1_dim),
            nn.Sigmoid()
        )
        
        # 模态2的门控网络
        self.gate2_network = nn.Sequential(
            nn.Linear(hidden_dim // 2, modal2_dim),
            nn.Sigmoid()
        )
        
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化权重
        """
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, modal1_features: torch.Tensor, modal2_features: torch.Tensor) -> tuple:
        """
        前向传播
        
        Args:
            modal1_features: 模态1特征 [batch_size, modal1_dim]
            modal2_features: 模态2特征 [batch_size, modal2_dim]
            
        Returns:
            (门控后的模态1特征, 门控后的模态2特征)
        """
        # 拼接特征
        combined_features = torch.cat([modal1_features, modal2_features], dim=-1)
        
        # 跨模态交互
        interaction_features = self.interaction_network(combined_features)
        
        # 计算门控值
        gate1_values = self.gate1_network(interaction_features)  # [batch_size, modal1_dim]
        gate2_values = self.gate2_network(interaction_features)  # [batch_size, modal2_dim]
        
        # 应用门控
        gated_modal1 = gate1_values * modal1_features
        gated_modal2 = gate2_values * modal2_features
        
        return gated_modal1, gated_modal2


class AdaptiveFusionGate(nn.Module):
    """
    自适应融合门控单元
    """
    
    def __init__(self, feature_dim: int, num_modalities: int, hidden_dim: int, dropout: float = 0.1):
        """
        初始化自适应融合门控单元
        
        Args:
            feature_dim: 特征维度
            num_modalities: 模态数量
            hidden_dim: 隐藏层维度
            dropout: Dropout率
        """
        super(AdaptiveFusionGate, self).__init__()
        
        self.feature_dim = feature_dim
        self.num_modalities = num_modalities
        self.hidden_dim = hidden_dim
        
        # 全局上下文网络
        self.context_network = nn.Sequential(
            nn.Linear(feature_dim * num_modalities, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 模态重要性评估网络
        self.importance_network = nn.Sequential(
            nn.Linear(hidden_dim // 2, num_modalities),
            nn.Softmax(dim=-1)  # 输出模态权重分布
        )
        
        # 特征增强网络
        self.enhancement_networks = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim, feature_dim),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(feature_dim, feature_dim),
                nn.LayerNorm(feature_dim)
            ) for _ in range(num_modalities)
        ])
        
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化权重
        """
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, modal_features: List[torch.Tensor]) -> tuple:
        """
        前向传播
        
        Args:
            modal_features: 模态特征列表，每个元素为 [batch_size, feature_dim]
            
        Returns:
            (融合特征, 模态权重)
        """
        batch_size = modal_features[0].size(0)
        
        # 拼接所有模态特征
        all_features = torch.cat(modal_features, dim=-1)  # [batch_size, feature_dim * num_modalities]
        
        # 计算全局上下文
        context = self.context_network(all_features)  # [batch_size, hidden_dim // 2]
        
        # 计算模态重要性权重
        modal_weights = self.importance_network(context)  # [batch_size, num_modalities]
        
        # 特征增强和加权融合
        enhanced_features = []
        for i, (features, enhancement_net) in enumerate(zip(modal_features, self.enhancement_networks)):
            # 特征增强
            enhanced = enhancement_net(features)  # [batch_size, feature_dim]
            
            # 应用权重
            weight = modal_weights[:, i:i+1]  # [batch_size, 1]
            weighted_enhanced = weight * enhanced
            
            enhanced_features.append(weighted_enhanced)
        
        # 融合所有增强特征
        fused_features = torch.stack(enhanced_features, dim=1).sum(dim=1)  # [batch_size, feature_dim]
        
        return fused_features, modal_weights


class GateFusion(nn.Module):
    """
    基于门控机制的多模态融合模块
    """
    
    def __init__(self, config: Dict):
        """
        初始化门控融合模块
        
        Args:
            config: 配置字典，包含：
                - fusion_dim: 融合特征维度
                - dropout: Dropout率
                - output_dim: 输出维度
        """
        super(GateFusion, self).__init__()
        
        # 从配置中提取参数
        self.fusion_dim = config.get('fusion_dim', 768)
        self.dropout = config.get('dropout', 0.1)
        self.output_dim = config.get('output_dim', 512)
        
        # 特征维度统一
        self.input_dim = 256  # 假设所有模态输入维度为256
        
        # 模态特征投影层
        self.sequence_projection = nn.Linear(self.input_dim, self.fusion_dim)
        self.image_projection = nn.Linear(self.input_dim, self.fusion_dim)
        self.graph_projection = nn.Linear(self.input_dim, self.fusion_dim)
        
        # 单模态门控单元
        self.sequence_gate = ModalityGate(self.fusion_dim, self.fusion_dim, self.dropout)
        self.image_gate = ModalityGate(self.fusion_dim, self.fusion_dim, self.dropout)
        self.graph_gate = ModalityGate(self.fusion_dim, self.fusion_dim, self.dropout)
        
        # 跨模态门控单元
        self.seq_img_gate = CrossModalGate(self.fusion_dim, self.fusion_dim, self.fusion_dim, self.dropout)
        self.seq_graph_gate = CrossModalGate(self.fusion_dim, self.fusion_dim, self.fusion_dim, self.dropout)
        self.img_graph_gate = CrossModalGate(self.fusion_dim, self.fusion_dim, self.fusion_dim, self.dropout)
        
        # 自适应融合门控
        self.adaptive_gate = AdaptiveFusionGate(self.fusion_dim, 3, self.fusion_dim, self.dropout)
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(self.fusion_dim, self.fusion_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.fusion_dim // 2, self.output_dim),
            nn.LayerNorm(self.output_dim)
        )
        
        # 残差连接权重
        self.residual_weight = nn.Parameter(torch.tensor(0.1))
    
    def forward(self, sequence_features: torch.Tensor, image_features: torch.Tensor, 
                graph_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            sequence_features: 序列特征 [batch_size, input_dim]
            image_features: 图像特征 [batch_size, input_dim]
            graph_features: 图特征 [batch_size, input_dim]
            
        Returns:
            融合后的特征 [batch_size, output_dim]
        """
        # 特征投影到统一维度
        seq_proj = self.sequence_projection(sequence_features)
        img_proj = self.image_projection(image_features)
        graph_proj = self.graph_projection(graph_features)
        
        # 单模态门控
        seq_gate_val, seq_gated = self.sequence_gate(seq_proj)
        img_gate_val, img_gated = self.image_gate(img_proj)
        graph_gate_val, graph_gated = self.graph_gate(graph_proj)
        
        # 跨模态门控交互
        seq_img_gated, img_seq_gated = self.seq_img_gate(seq_gated, img_gated)
        seq_graph_gated, graph_seq_gated = self.seq_graph_gate(seq_gated, graph_gated)
        img_graph_gated, graph_img_gated = self.img_graph_gate(img_gated, graph_gated)
        
        # 融合跨模态交互结果
        seq_enhanced = seq_gated + seq_img_gated + seq_graph_gated
        img_enhanced = img_gated + img_seq_gated + img_graph_gated
        graph_enhanced = graph_gated + graph_seq_gated + graph_img_gated
        
        # 自适应融合
        modal_features = [seq_enhanced, img_enhanced, graph_enhanced]
        fused_features, modal_weights = self.adaptive_gate(modal_features)
        
        # 残差连接
        residual = (seq_proj + img_proj + graph_proj) / 3
        fused_features = fused_features + self.residual_weight * residual
        
        # 输出投影
        output = self.output_projection(fused_features)
        
        return output
    
    def get_gate_values(self, sequence_features: torch.Tensor, image_features: torch.Tensor, 
                       graph_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        获取门控值（用于分析）
        
        Args:
            sequence_features: 序列特征
            image_features: 图像特征
            graph_features: 图特征
            
        Returns:
            门控值字典
        """
        # 特征投影
        seq_proj = self.sequence_projection(sequence_features)
        img_proj = self.image_projection(image_features)
        graph_proj = self.graph_projection(graph_features)
        
        # 获取单模态门控值
        seq_gate_val, _ = self.sequence_gate(seq_proj)
        img_gate_val, _ = self.image_gate(img_proj)
        graph_gate_val, _ = self.graph_gate(graph_proj)
        
        # 获取自适应权重
        modal_features = [seq_proj, img_proj, graph_proj]
        _, modal_weights = self.adaptive_gate(modal_features)
        
        return {
            'sequence_gate': seq_gate_val,
            'image_gate': img_gate_val,
            'graph_gate': graph_gate_val,
            'adaptive_weights': modal_weights,
            'residual_weight': self.residual_weight
        }


if __name__ == "__main__":
    # 测试代码
    config = {
        'fusion_dim': 512,
        'dropout': 0.1,
        'output_dim': 256
    }
    
    fusion_module = GateFusion(config)
    
    # 测试数据
    batch_size = 4
    sequence_features = torch.randn(batch_size, 256)
    image_features = torch.randn(batch_size, 256)
    graph_features = torch.randn(batch_size, 256)
    
    # 前向传播
    fused_output = fusion_module(sequence_features, image_features, graph_features)
    print(f"Fused output shape: {fused_output.shape}")
    
    # 获取门控值
    gate_values = fusion_module.get_gate_values(sequence_features, image_features, graph_features)
    print(f"Gate values keys: {gate_values.keys()}")
    print(f"Adaptive weights shape: {gate_values['adaptive_weights'].shape}")
    print(f"Adaptive weights: {gate_values['adaptive_weights'][0]}")  # 第一个样本的权重
