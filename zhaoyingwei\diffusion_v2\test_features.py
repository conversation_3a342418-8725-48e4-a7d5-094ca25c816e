"""
Test script for the new features in diffusion_v2.
Tests custom log directory and ImageFolder dataset loading.
"""

import os
import sys
import tempfile
import shutil
from PIL import Image
import numpy as np

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_dataset(base_dir, num_classes=3, images_per_class=5):
    """Create a test ImageFolder dataset"""
    print(f"📁 Creating test dataset in {base_dir}")
    
    class_names = [f"class_{i}" for i in range(num_classes)]
    
    for class_name in class_names:
        class_dir = os.path.join(base_dir, class_name)
        os.makedirs(class_dir, exist_ok=True)
        
        for i in range(images_per_class):
            # Create a simple colored image for each class
            img = np.random.randint(0, 255, (64, 64, 3), dtype=np.uint8)
            # Add some class-specific pattern
            class_id = int(class_name.split('_')[1])
            img[:, :, class_id % 3] = 255  # Make one channel bright for each class
            
            pil_img = Image.fromarray(img)
            img_path = os.path.join(class_dir, f"image_{i:03d}.png")
            pil_img.save(img_path)
    
    print(f"✅ Created {num_classes} classes with {images_per_class} images each")
    return class_names


def test_imagefolder_loading():
    """Test ImageFolder dataset loading"""
    print("\n🔍 Testing ImageFolder dataset loading...")
    
    # Create temporary dataset
    with tempfile.TemporaryDirectory() as temp_dir:
        class_names = create_test_dataset(temp_dir, num_classes=3, images_per_class=5)
        
        try:
            from diffusion_v2.image_datasets import load_data
            
            # Test loading with ImageFolder format
            data_loader = load_data(
                data_dir=temp_dir,
                batch_size=4,
                image_size=64,
                class_cond=True,
                use_imagefolder=True,
                deterministic=True
            )
            
            # Get a batch
            batch = next(iter(data_loader))
            images, labels_dict = batch
            
            print(f"✅ Successfully loaded batch:")
            print(f"   Images shape: {images.shape}")
            print(f"   Labels shape: {labels_dict['y'].shape}")
            print(f"   Label values: {labels_dict['y'].numpy()}")
            print(f"   Expected classes: {class_names}")
            
            return True
            
        except Exception as e:
            print(f"❌ ImageFolder loading failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def test_cfg_training_logic():
    """Test CFG training logic without actual training"""
    print("\n🔍 Testing CFG training logic...")
    
    try:
        import torch as th
        from diffusion_v2.gaussian_diffusion import GaussianDiffusion, ModelMeanType, ModelVarType, LossType
        
        # Create a simple diffusion process
        betas = np.linspace(0.0001, 0.02, 100)
        diffusion = GaussianDiffusion(
            betas=betas,
            model_mean_type=ModelMeanType.EPSILON,
            model_var_type=ModelVarType.FIXED_SMALL,
            loss_type=LossType.MSE,
        )
        
        # Create dummy data
        batch_size = 4
        x_start = th.randn(batch_size, 3, 32, 32)
        t = th.randint(0, 100, (batch_size,))
        y = th.randint(0, 3, (batch_size,))  # 3 classes
        
        model_kwargs = {"y": y}
        
        # Test CFG dropout logic (this should modify y in-place)
        print(f"   Original labels: {y.numpy()}")
        
        # Simulate training_losses call with CFG
        # Note: We can't actually call training_losses without a model,
        # but we can test the logic
        cfg_dropout_prob = 0.5
        if "y" in model_kwargs and cfg_dropout_prob > 0:
            # Create mask for dropping labels
            drop_mask = th.rand(batch_size) < cfg_dropout_prob
            # Create unconditional labels
            y_uncond = th.full_like(model_kwargs["y"], -1)
            # Apply mask
            model_kwargs = dict(model_kwargs)
            model_kwargs["y"] = th.where(drop_mask, y_uncond, model_kwargs["y"])
            
            print(f"   After CFG dropout: {model_kwargs['y'].numpy()}")
            print(f"   Drop mask: {drop_mask.numpy()}")
            print(f"   Dropout probability: {cfg_dropout_prob}")
        
        print("✅ CFG training logic test passed")
        return True
        
    except Exception as e:
        print(f"❌ CFG training logic test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_log_directory():
    """Test custom log directory functionality"""
    print("\n🔍 Testing custom log directory...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        log_dir = os.path.join(temp_dir, "test_logs")
        
        try:
            from diffusion_v2 import logger
            
            # Test logger configuration
            logger.configure(dir=log_dir)
            
            # Check if directory was created
            if os.path.exists(log_dir):
                print(f"✅ Log directory created: {log_dir}")
                
                # Test logging
                logger.log("Test log message")
                logger.logkv("test_key", 42)
                
                print("✅ Logging functionality works")
                return True
            else:
                print(f"❌ Log directory not created")
                return False
                
        except Exception as e:
            print(f"❌ Log directory test failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def test_model_cfg_handling():
    """Test UNet model CFG label handling"""
    print("\n🔍 Testing UNet CFG label handling...")
    
    try:
        import torch as th
        from diffusion_v2.unet import UNetModel
        
        # Create a small UNet model
        model = UNetModel(
            in_channels=3,
            model_channels=32,
            out_channels=3,
            num_res_blocks=1,
            attention_resolutions="",
            num_classes=3,  # Enable class conditioning
        )
        
        # Test data
        batch_size = 2
        x = th.randn(batch_size, 3, 32, 32)
        timesteps = th.randint(0, 1000, (batch_size,))
        
        # Test with normal labels
        y_normal = th.tensor([0, 1])
        output_normal = model(x, timesteps, y_normal)
        print(f"✅ Normal labels work, output shape: {output_normal.shape}")
        
        # Test with unconditional labels (-1)
        y_uncond = th.tensor([-1, -1])
        output_uncond = model(x, timesteps, y_uncond)
        print(f"✅ Unconditional labels work, output shape: {output_uncond.shape}")
        
        # Test with mixed labels
        y_mixed = th.tensor([0, -1])
        output_mixed = model(x, timesteps, y_mixed)
        print(f"✅ Mixed labels work, output shape: {output_mixed.shape}")
        
        print("✅ UNet CFG label handling test passed")
        return True
        
    except Exception as e:
        print(f"❌ UNet CFG label handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("🧪 Testing Diffusion V2 New Features")
    print("=" * 60)
    
    tests = [
        ("ImageFolder Dataset Loading", test_imagefolder_loading),
        ("CFG Training Logic", test_cfg_training_logic),
        ("Custom Log Directory", test_log_directory),
        ("UNet CFG Label Handling", test_model_cfg_handling),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The new features are working correctly.")
        print("\n📋 Next steps:")
        print("1. Try the example_usage.py script")
        print("2. Test with your own dataset")
        print("3. Experiment with different CFG settings")
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
