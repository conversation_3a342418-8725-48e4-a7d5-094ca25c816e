"""
自动化数据预处理管道 - 直接处理原始PCAP文件生成多模态数据
整合现有ProcessPCAP方法，自动提取序列、图像和图数据
"""

import os
import sys
import logging
import pickle
import shutil
import random

from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import yaml

# 添加ProcessPCAP路径
current_dir = Path(__file__).parent.parent
process_pcap_dir = current_dir.parent / "ProcessPCAP"
if str(process_pcap_dir) not in sys.path:
    sys.path.insert(0, str(process_pcap_dir))

# 导入现有的ProcessPCAP组件
try:
    from session_splitter import SessionSplitter
    from traffic_anonymizer import TrafficAnonymizer
    from rgb_generator import RGBGenerator
except ImportError as e:
    print(f"警告：无法导入ProcessPCAP组件: {e}")
    print("请确保ProcessPCAP目录存在且包含必要的模块")

# 导入本地数据处理器
from .pcap_processor import PCAPProcessor
from .sequence_processor import SequenceProcessor
from .graph_processor import GraphProcessor


class AutomatedPreprocessingPipeline:
    """
    自动化预处理管道，将原始PCAP文件转换为多模态训练数据
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化自动化预处理管道
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        else:
            self.config = self._get_default_config()
        
        # 设置路径
        self.input_dir = Path(self.config['input']['raw_pcap_dir'])
        self.output_dir = Path(self.config['output']['processed_data_dir'])
        self.temp_dir = Path(self.config['temp']['temp_dir'])
        
        # 创建必要目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 初始化处理器
        self._init_processors()
        
        self.logger.info("✅ 自动化预处理管道初始化完成")
        self.logger.info(f"   输入目录: {self.input_dir}")
        self.logger.info(f"   输出目录: {self.output_dir}")
        self.logger.info(f"   临时目录: {self.temp_dir}")
    
    def _get_default_config(self) -> Dict:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            'input': {
                'raw_pcap_dir': './raw_data',
                'supported_extensions': ['.pcap', '.pcapng']
            },
            'output': {
                'processed_data_dir': './processed_data',
                'train_ratio': 0.7,
                'val_ratio': 0.15,
                'test_ratio': 0.15
            },
            'temp': {
                'temp_dir': './temp_processing',
                'cleanup_temp': True
            },
            'processing': {
                'max_sessions_per_pcap': 1000,
                'image_size': 32,
                'sequence_length': 512,
                'graph_max_nodes': 100,
                'min_packets_per_session': 10,
                'max_file_size_mb': 100
            },
            'parallel': {
                'num_workers': 4,
                'batch_size': 10
            },
            'validation': {
                'check_corrupted_files': True,
                'min_file_size_bytes': 1024,
                'max_processing_time_minutes': 30
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """
        设置日志记录器
        
        Returns:
            配置好的日志记录器
        """
        logger = logging.getLogger('AutomatedPreprocessing')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件处理器
            log_file = self.output_dir / 'preprocessing.log'
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(console_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def _init_processors(self):
        """
        初始化各种数据处理器
        """
        # ProcessPCAP组件
        self.session_splitter = SessionSplitter(
            output_base_dir=str(self.temp_dir / "split"),
            max_sessions=self.config['processing']['max_sessions_per_pcap']
        )
        
        # TrafficAnonymizer 会在运行时动态设置输入目录
        self.traffic_anonymizer = TrafficAnonymizer(
            input_base_dir=str(self.temp_dir),
            output_base_dir=str(self.temp_dir / "anonymous")
        )
        
        # RGBGenerator 会在运行时动态设置输入目录
        self.rgb_generator = RGBGenerator(
            input_base_dir=str(self.temp_dir),
            output_base_dir=str(self.temp_dir / "rgb"),
            image_size=self.config['processing']['image_size']
        )
        
        # 多模态数据处理器
        self.pcap_processor = PCAPProcessor(self.config['processing'])
        self.sequence_processor = SequenceProcessor({
            'sequence_length': self.config['processing']['sequence_length'],
            'vocab_size': 10000,
            'embedding_dim': 256
        })
        self.graph_processor = GraphProcessor({
            'graph_max_nodes': self.config['processing']['graph_max_nodes'],
            'node_feature_dim': 64,
            'edge_feature_dim': 2
        })
    
    def validate_input_data(self) -> Tuple[List[Path], List[str]]:
        """
        验证输入数据的有效性
        
        Returns:
            (有效文件列表, 错误信息列表)
        """
        self.logger.info("🔍 验证输入数据...")
        
        valid_files = []
        errors = []
        
        if not self.input_dir.exists():
            errors.append(f"输入目录不存在: {self.input_dir}")
            return valid_files, errors
        
        # 查找所有PCAP文件
        extensions = self.config['input']['supported_extensions']
        all_pcap_files = []
        for ext in extensions:
            all_pcap_files.extend(self.input_dir.rglob(f"*{ext}"))
        
        if not all_pcap_files:
            errors.append(f"在 {self.input_dir} 中未找到PCAP文件")
            return valid_files, errors
        
        # 验证每个文件
        min_size = self.config['validation']['min_file_size_bytes']
        max_size_mb = self.config['processing']['max_file_size_mb']
        max_size_bytes = max_size_mb * 1024 * 1024
        
        for pcap_file in tqdm(all_pcap_files, desc="验证PCAP文件"):
            try:
                # 检查文件大小
                file_size = pcap_file.stat().st_size
                if file_size < min_size:
                    errors.append(f"文件太小: {pcap_file} ({file_size} bytes)")
                    continue
                
                if file_size > max_size_bytes:
                    self.logger.warning(f"文件较大，可能处理缓慢: {pcap_file} ({file_size/1024/1024:.1f} MB)")
                
                # 检查文件是否可读
                if self.config['validation']['check_corrupted_files']:
                    try:
                        from scapy.all import rdpcap
                        # 只读取前几个包来验证文件格式
                        packets = rdpcap(str(pcap_file), count=5)
                        if len(packets) == 0:
                            errors.append(f"PCAP文件为空: {pcap_file}")
                            continue
                    except Exception as e:
                        errors.append(f"PCAP文件损坏: {pcap_file} - {e}")
                        continue
                
                valid_files.append(pcap_file)
                
            except Exception as e:
                errors.append(f"验证文件时出错 {pcap_file}: {e}")
        
        self.logger.info(f"✅ 验证完成: {len(valid_files)} 个有效文件, {len(errors)} 个错误")
        
        if errors:
            self.logger.warning("发现以下错误:")
            for error in errors[:10]:  # 只显示前10个错误
                self.logger.warning(f"  - {error}")
            if len(errors) > 10:
                self.logger.warning(f"  ... 还有 {len(errors) - 10} 个错误")
        
        return valid_files, errors
    
    def organize_files_by_class(self, valid_files: List[Path]) -> Dict[str, List[Path]]:
        """
        按类别组织文件 - 适配扁平文件结构
        每个PCAP文件代表一个完整的类别

        Args:
            valid_files: 有效文件列表

        Returns:
            按类别组织的文件字典
        """
        self.logger.info("📁 按类别组织文件（扁平结构）...")

        class_files = {}

        for pcap_file in valid_files:
            # 从文件名（不含扩展名）提取类别名
            class_name = pcap_file.stem

            # 清理类别名称（移除可能的特殊字符）
            class_name = class_name.replace(' ', '_').replace('-', '_')

            if class_name not in class_files:
                class_files[class_name] = []

            class_files[class_name].append(pcap_file)

        # 打印统计信息
        self.logger.info(f"发现 {len(class_files)} 个类别:")
        for class_name, files in class_files.items():
            self.logger.info(f"  - {class_name}: {len(files)} 个PCAP文件")

        return class_files

    def extract_all_sessions(self, class_files: Dict[str, List[Path]]) -> Dict[str, List[Dict]]:
        """
        从所有PCAP文件中提取会话信息

        Args:
            class_files: 按类别组织的文件字典

        Returns:
            按类别组织的会话信息字典
        """
        self.logger.info("🔍 提取所有会话信息...")

        class_sessions = {}

        for class_name, pcap_files in class_files.items():
            self.logger.info(f"  处理类别 {class_name}: {len(pcap_files)} 个PCAP文件")

            class_sessions[class_name] = []

            for pcap_file in pcap_files:
                try:
                    # 预处理单个PCAP文件获取会话信息
                    sessions = self._extract_sessions_from_pcap(pcap_file, class_name)
                    class_sessions[class_name].extend(sessions)

                except Exception as e:
                    self.logger.warning(f"提取会话失败 {pcap_file}: {e}")
                    continue

            self.logger.info(f"  {class_name}: 提取了 {len(class_sessions[class_name])} 个会话")

        return class_sessions

    def split_train_val_test_sessions(self, class_sessions: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        """
        在会话级别分割训练、验证和测试集

        Args:
            class_sessions: 按类别组织的会话信息字典

        Returns:
            分割后的会话字典 {'train': [...], 'val': [...], 'test': [...]}
        """
        self.logger.info("✂️ 在会话级别分割训练/验证/测试集...")

        train_ratio = self.config['output']['train_ratio']
        val_ratio = self.config['output']['val_ratio']
        test_ratio = self.config['output']['test_ratio']

        # 验证比例
        total_ratio = train_ratio + val_ratio + test_ratio
        if abs(total_ratio - 1.0) > 0.01:
            self.logger.warning(f"数据集分割比例总和不为1: {total_ratio}")

        # 收集所有会话并标记类别
        all_sessions = []
        for class_name, sessions in class_sessions.items():
            for session in sessions:
                session['class_name'] = class_name
                all_sessions.append(session)

        # 随机打乱所有会话
        random.shuffle(all_sessions)

        # 计算分割点
        total_sessions = len(all_sessions)
        n_train = int(total_sessions * train_ratio)
        n_val = int(total_sessions * val_ratio)
        n_test = total_sessions - n_train - n_val

        # 分割会话
        split_sessions = {
            'train': all_sessions[:n_train],
            'val': all_sessions[n_train:n_train + n_val],
            'test': all_sessions[n_train + n_val:]
        }

        # 打印分割统计
        self.logger.info(f"会话分割结果:")
        self.logger.info(f"  训练集: {len(split_sessions['train'])} 个会话")
        self.logger.info(f"  验证集: {len(split_sessions['val'])} 个会话")
        self.logger.info(f"  测试集: {len(split_sessions['test'])} 个会话")

        # 打印各类别在各分割中的分布
        for split_name, sessions in split_sessions.items():
            class_counts = {}
            for session in sessions:
                class_name = session['class_name']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1

            self.logger.info(f"  {split_name} 类别分布: {class_counts}")

        return split_sessions

    def _extract_sessions_from_pcap(self, pcap_file: Path, class_name: str) -> List[Dict]:
        """
        从单个PCAP文件中提取会话信息

        Args:
            pcap_file: PCAP文件路径
            class_name: 类别名称

        Returns:
            会话信息列表
        """
        sessions = []

        try:
            # 创建临时目录用于会话分割
            temp_session_dir = self.temp_dir / "session_extraction" / class_name / pcap_file.stem
            temp_session_dir.mkdir(parents=True, exist_ok=True)

            # 使用会话分割器处理PCAP文件
            try:
                self.session_splitter.split_pcap_by_sessions(
                    str(pcap_file),
                    str(temp_session_dir)
                )
                success = True
            except Exception as e:
                self.logger.warning(f"会话分割失败: {pcap_file} - {e}")
                success = False

            if not success:
                return sessions

            # 查找生成的会话文件（在al子目录中）
            al_dir = temp_session_dir / "al"
            if not al_dir.exists():
                self.logger.warning(f"AL目录不存在: {al_dir}")
                return sessions

            session_files = list(al_dir.glob("*.pcap"))

            for i, session_file in enumerate(session_files):
                # 检查会话质量
                try:
                    from scapy.all import rdpcap
                    packets = rdpcap(str(session_file), count=self.config['processing']['min_packets_per_session'] + 1)

                    if len(packets) >= self.config['processing']['min_packets_per_session']:
                        session_info = {
                            'original_pcap': pcap_file,
                            'session_file': session_file,
                            'session_id': f"{pcap_file.stem}_session_{i:03d}",
                            'class_name': class_name,
                            'packet_count': len(packets)
                        }
                        sessions.append(session_info)

                except Exception as e:
                    self.logger.debug(f"跳过无效会话 {session_file}: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"提取会话时出错 {pcap_file}: {e}")

        return sessions



    def _save_multimodal_sample(self, output_dir: Path, class_name: str, sample_id: str,
                               sequence_data: Dict, graph_data: Dict, rgb_image_path: Path):
        """
        保存多模态样本数据

        Args:
            output_dir: 输出目录
            class_name: 类别名称
            sample_id: 样本ID
            sequence_data: 序列数据
            graph_data: 图数据
            rgb_image_path: RGB图像路径
        """
        # 创建类别目录
        class_output_dir = output_dir / class_name
        class_output_dir.mkdir(parents=True, exist_ok=True)

        # 保存序列和图数据到pickle文件
        multimodal_data = {
            'sequence': sequence_data,
            'graph': graph_data,
            'sample_id': sample_id,
            'class_name': class_name
        }

        pkl_path = class_output_dir / f"{sample_id}.pkl"
        with open(pkl_path, 'wb') as f:
            pickle.dump(multimodal_data, f)

        # 复制RGB图像
        img_path = class_output_dir / f"{sample_id}.png"
        shutil.copy2(rgb_image_path, img_path)

        self.logger.debug(f"保存样本: {pkl_path}, {img_path}")

    def process_dataset_sessions(self, split_sessions: Dict[str, List[Dict]]) -> Dict[str, int]:
        """
        处理会话级别分割的数据集

        Args:
            split_sessions: 分割后的会话字典

        Returns:
            处理统计信息
        """
        self.logger.info("🚀 开始处理会话级别数据集...")

        stats = {'train': 0, 'val': 0, 'test': 0, 'total': 0, 'errors': 0}

        for split_name in ['train', 'val', 'test']:
            self.logger.info(f"\n📊 处理 {split_name} 数据集...")

            split_output_dir = self.output_dir / split_name
            split_output_dir.mkdir(parents=True, exist_ok=True)

            sessions = split_sessions[split_name]

            if not sessions:
                self.logger.warning(f"{split_name} 数据集为空")
                continue

            processed_count = 0

            # 按类别组织会话以便显示进度
            class_sessions = {}
            for session in sessions:
                class_name = session['class_name']
                if class_name not in class_sessions:
                    class_sessions[class_name] = []
                class_sessions[class_name].append(session)

            # 处理每个类别的会话
            for class_name, class_session_list in class_sessions.items():
                self.logger.info(f"  处理类别 {class_name}: {len(class_session_list)} 个会话")

                class_progress = tqdm(class_session_list, desc=f"{split_name}/{class_name}")

                for session_info in class_progress:
                    try:
                        # 处理单个会话
                        success = self.process_single_session(
                            session_info, split_output_dir
                        )

                        if success:
                            processed_count += 1

                        class_progress.set_postfix({
                            'processed': processed_count
                        })

                    except Exception as e:
                        self.logger.error(f"处理会话失败 {session_info['session_id']}: {e}")
                        stats['errors'] += 1

            stats[split_name] = processed_count
            stats['total'] += processed_count

            self.logger.info(f"✅ {split_name} 数据集处理完成: {processed_count} 个样本")

        return stats

    def process_single_session(self, session_info: Dict, output_dir: Path) -> bool:
        """
        处理单个会话，生成多模态数据

        Args:
            session_info: 会话信息字典
            output_dir: 输出目录

        Returns:
            是否处理成功
        """
        try:
            session_file = session_info['session_file']
            session_id = session_info['session_id']
            class_name = session_info['class_name']

            self.logger.debug(f"处理会话 {session_id}")

            # 1. 流量匿名化
            anon_output_dir = self.temp_dir / "anonymous" / class_name / session_id
            anon_output_dir.mkdir(parents=True, exist_ok=True)

            # 创建单个会话的临时目录结构（需要包含al子目录）
            session_temp_dir = self.temp_dir / "single_session" / class_name / session_id
            al_dir = session_temp_dir / "al"
            al_dir.mkdir(parents=True, exist_ok=True)

            # 复制会话文件到al子目录
            temp_session_file = al_dir / session_file.name
            shutil.copy2(session_file, temp_session_file)

            # 匿名化处理
            try:
                # 创建一个临时的 TrafficAnonymizer 实例，使用正确的路径
                temp_anonymizer = TrafficAnonymizer(
                    input_base_dir=str(self.temp_dir / "single_session"),
                    output_base_dir=str(self.temp_dir / "anonymous")
                )
                temp_anonymizer.anonymize_session_files(str(session_temp_dir))
                success = True
            except Exception as e:
                self.logger.warning(f"流量匿名化失败: {session_id} - {e}")
                success = False

            if not success:
                self.logger.warning(f"流量匿名化失败: {session_id}")
                return False

            # 2. 生成RGB图像
            # 匿名化后的文件应该在 anonymous 目录下
            anon_session_dir = self.temp_dir / "anonymous" / class_name / session_id

            try:
                # 创建一个临时的 RGBGenerator 实例，使用正确的路径
                temp_rgb_generator = RGBGenerator(
                    input_base_dir=str(self.temp_dir / "anonymous"),
                    output_base_dir=str(self.temp_dir / "rgb"),
                    image_size=self.config['processing']['image_size']
                )
                temp_rgb_generator.process_session_directory(str(anon_session_dir))
                success = True
            except Exception as e:
                self.logger.warning(f"RGB图像生成失败: {session_id} - {e}")
                success = False

            if not success:
                self.logger.warning(f"RGB图像生成失败: {session_id}")
                return False

            # 3. 提取多模态数据
            success = self._extract_and_save_multimodal_data(
                session_info, anon_session_dir, output_dir
            )

            return success

        except Exception as e:
            self.logger.error(f"处理会话时出错 {session_info.get('session_id', 'unknown')}: {e}")
            return False

    def _extract_and_save_multimodal_data(self, session_info: Dict, anon_dir: Path, output_dir: Path) -> bool:
        """
        从匿名化会话数据中提取并保存多模态特征

        Args:
            session_info: 会话信息
            anon_dir: 匿名化数据目录
            output_dir: 输出目录

        Returns:
            是否成功
        """
        try:
            session_id = session_info['session_id']
            class_name = session_info['class_name']

            # 查找匿名化后的会话文件
            al_dir = anon_dir / "al"
            if not al_dir.exists():
                self.logger.warning(f"AL目录不存在: {al_dir}")
                return False

            session_files = list(al_dir.glob("*.pcap"))
            if not session_files:
                self.logger.warning(f"未找到匿名化会话文件: {al_dir}")
                return False

            # 通常只有一个会话文件
            session_file = session_files[0]

            # 提取序列和图数据
            pcap_data = self.pcap_processor.process_pcap_file(str(session_file))
            if pcap_data is None:
                self.logger.warning(f"PCAP数据提取失败: {session_file}")
                return False

            # 检查数据质量
            if pcap_data['packet_count'] < self.config['processing']['min_packets_per_session']:
                self.logger.debug(f"会话包数不足: {pcap_data['packet_count']}")
                return False

            # 处理序列数据
            sequence_data = self.sequence_processor.process_sequence(pcap_data['sequence'])

            # 处理图数据
            graph_data = self.graph_processor.process_graph(pcap_data['graph'])

            # 查找对应的RGB图像
            rgb_image_path = self._find_rgb_image_for_session(anon_dir, session_file.stem)

            if not rgb_image_path or not rgb_image_path.exists():
                self.logger.warning(f"未找到RGB图像: {session_id}")
                return False

            # 保存多模态数据
            self._save_multimodal_sample(
                output_dir, class_name, session_id,
                sequence_data, graph_data, rgb_image_path
            )

            return True

        except Exception as e:
            self.logger.error(f"提取多模态数据时出错: {e}")
            return False

    def _find_rgb_image_for_session(self, anon_dir: Path, session_name: str) -> Optional[Path]:
        """
        查找会话对应的RGB图像文件

        Args:
            anon_dir: 匿名化数据目录
            session_name: 会话名称

        Returns:
            RGB图像路径或None
        """
        # RGB图像通常保存在rgb目录下
        rgb_base_dir = self.temp_dir / "rgb"

        # 可能的图像路径
        possible_paths = [
            rgb_base_dir / f"{session_name}.png",
            rgb_base_dir / f"{session_name}.jpg",
            anon_dir.parent / "rgb" / f"{session_name}.png",
            anon_dir.parent / "rgb" / f"{session_name}.jpg",
            # 也可能在类别子目录下
            rgb_base_dir / anon_dir.parent.name / f"{session_name}.png",
            rgb_base_dir / anon_dir.parent.name / f"{session_name}.jpg"
        ]

        for path in possible_paths:
            if path.exists():
                return path

        # 如果找不到精确匹配，尝试模糊匹配
        for rgb_dir in [rgb_base_dir, anon_dir.parent / "rgb"]:
            if rgb_dir.exists():
                for img_file in rgb_dir.rglob("*.png"):
                    if session_name in img_file.stem:
                        return img_file
                for img_file in rgb_dir.rglob("*.jpg"):
                    if session_name in img_file.stem:
                        return img_file

        return None

    def cleanup_temp_files(self):
        """
        清理临时文件
        """
        if self.config['temp']['cleanup_temp'] and self.temp_dir.exists():
            self.logger.info("🧹 清理临时文件...")
            try:
                shutil.rmtree(self.temp_dir)
                self.logger.info("✅ 临时文件清理完成")
            except Exception as e:
                self.logger.warning(f"清理临时文件失败: {e}")

    def generate_dataset_info(self, stats: Dict[str, int]):
        """
        生成数据集信息文件

        Args:
            stats: 处理统计信息
        """
        self.logger.info("📋 生成数据集信息...")

        # 统计各分割的类别和样本数
        dataset_info = {
            'total_samples': stats['total'],
            'splits': {},
            'classes': {},
            'processing_config': self.config
        }

        for split_name in ['train', 'val', 'test']:
            split_dir = self.output_dir / split_name
            if not split_dir.exists():
                continue

            split_info = {'total_samples': 0, 'classes': {}}

            for class_dir in split_dir.iterdir():
                if class_dir.is_dir():
                    class_name = class_dir.name
                    pkl_files = list(class_dir.glob("*.pkl"))
                    sample_count = len(pkl_files)

                    split_info['classes'][class_name] = sample_count
                    split_info['total_samples'] += sample_count

                    # 更新全局类别统计
                    if class_name not in dataset_info['classes']:
                        dataset_info['classes'][class_name] = {'train': 0, 'val': 0, 'test': 0}
                    dataset_info['classes'][class_name][split_name] = sample_count

            dataset_info['splits'][split_name] = split_info

        # 保存数据集信息
        info_path = self.output_dir / 'dataset_info.yaml'
        with open(info_path, 'w', encoding='utf-8') as f:
            yaml.dump(dataset_info, f, default_flow_style=False, allow_unicode=True)

        self.logger.info(f"✅ 数据集信息已保存到: {info_path}")

        # 打印摘要
        self.logger.info("\n📊 数据集处理摘要:")
        self.logger.info(f"  总样本数: {dataset_info['total_samples']}")
        self.logger.info(f"  类别数: {len(dataset_info['classes'])}")

        for split_name, split_info in dataset_info['splits'].items():
            self.logger.info(f"  {split_name}: {split_info['total_samples']} 样本")

        self.logger.info("\n各类别样本分布:")
        for class_name, class_stats in dataset_info['classes'].items():
            total = sum(class_stats.values())
            self.logger.info(f"  {class_name}: {total} (训练:{class_stats['train']}, "
                           f"验证:{class_stats['val']}, 测试:{class_stats['test']})")

    def run_full_pipeline(self) -> bool:
        """
        运行完整的预处理管道（适配扁平文件结构）

        Returns:
            是否成功完成
        """
        try:
            self.logger.info("🚀 启动自动化数据预处理管道（扁平文件结构）")

            # 1. 验证输入数据
            valid_files, errors = self.validate_input_data()
            if not valid_files:
                self.logger.error("没有找到有效的PCAP文件")
                return False

            if len(errors) > len(valid_files) * 0.5:
                self.logger.error("错误文件数量过多，请检查数据质量")
                return False

            # 2. 按类别组织文件（每个PCAP文件代表一个类别）
            class_files = self.organize_files_by_class(valid_files)
            if not class_files:
                self.logger.error("无法组织文件")
                return False

            # 3. 从所有PCAP文件中提取会话信息
            class_sessions = self.extract_all_sessions(class_files)
            if not class_sessions:
                self.logger.error("未能提取到任何会话")
                return False

            # 4. 在会话级别分割数据集
            split_sessions = self.split_train_val_test_sessions(class_sessions)

            # 5. 处理会话级别的数据集
            stats = self.process_dataset_sessions(split_sessions)

            # 6. 生成数据集信息
            self.generate_dataset_info(stats)

            # 7. 清理临时文件
            self.cleanup_temp_files()

            self.logger.info("🎉 自动化预处理管道完成!")
            self.logger.info(f"   处理了 {stats['total']} 个会话样本")
            self.logger.info(f"   输出目录: {self.output_dir}")

            if stats['errors'] > 0:
                self.logger.warning(f"   发生了 {stats['errors']} 个错误")

            return True

        except Exception as e:
            self.logger.error(f"预处理管道失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """
    主函数 - 命令行接口
    """
    import argparse

    parser = argparse.ArgumentParser(description='自动化PCAP数据预处理管道')
    parser.add_argument('--input', type=str, required=True, help='原始PCAP数据目录')
    parser.add_argument('--output', type=str, required=True, help='处理后数据输出目录')
    parser.add_argument('--config', type=str, help='配置文件路径')
    parser.add_argument('--temp', type=str, default='./temp', help='临时文件目录')
    parser.add_argument('--workers', type=int, default=4, help='并行处理数量')
    parser.add_argument('--no-cleanup', action='store_true', help='不清理临时文件')

    args = parser.parse_args()

    # 创建配置
    if args.config and os.path.exists(args.config):
        config_path = args.config
    else:
        # 使用默认配置并更新路径
        config_path = None

    # 创建预处理管道
    pipeline = AutomatedPreprocessingPipeline(config_path)

    # 更新配置
    pipeline.config['input']['raw_pcap_dir'] = args.input
    pipeline.config['output']['processed_data_dir'] = args.output
    pipeline.config['temp']['temp_dir'] = args.temp
    pipeline.config['parallel']['num_workers'] = args.workers
    pipeline.config['temp']['cleanup_temp'] = not args.no_cleanup

    # 重新初始化路径
    pipeline.input_dir = Path(args.input)
    pipeline.output_dir = Path(args.output)
    pipeline.temp_dir = Path(args.temp)

    # 运行管道
    success = pipeline.run_full_pipeline()

    if success:
        print("\n✅ 预处理完成! 现在可以开始训练多模态模型了。")
        print(f"使用以下命令开始训练:")
        print(f"cd MultiModalProtoSimCLR")
        print(f"python experiments/train_multimodal.py --config config/multimodal_config.yaml")
    else:
        print("\n❌ 预处理失败，请检查日志文件。")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
