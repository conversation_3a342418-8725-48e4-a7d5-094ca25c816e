#!/usr/bin/env python3
"""
测试扁平文件结构的预处理功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from data.automated_preprocessing import AutomatedPreprocessingPipeline


def create_mock_pcap_files(temp_dir: Path, num_classes: int = 3) -> Path:
    """
    创建模拟的PCAP文件用于测试
    
    Args:
        temp_dir: 临时目录
        num_classes: 类别数量
        
    Returns:
        原始数据目录路径
    """
    raw_data_dir = temp_dir / "raw_data"
    raw_data_dir.mkdir(parents=True, exist_ok=True)
    
    class_names = ["normal", "ddos", "port_scan", "malware", "botnet"]
    
    for i in range(num_classes):
        class_name = class_names[i] if i < len(class_names) else f"class_{i+1}"
        pcap_file = raw_data_dir / f"{class_name}.pcap"
        
        # 创建空的PCAP文件（实际使用中应该是真实的PCAP数据）
        pcap_file.touch()
        
        print(f"创建模拟PCAP文件: {pcap_file}")
    
    return raw_data_dir


def test_flat_structure_organization():
    """
    测试扁平文件结构的组织功能
    """
    print("🧪 测试扁平文件结构组织...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建模拟数据
        raw_data_dir = create_mock_pcap_files(temp_path, num_classes=4)
        
        # 创建预处理管道
        config = {
            'input': {
                'raw_pcap_dir': str(raw_data_dir),
                'supported_extensions': ['.pcap', '.pcapng']
            },
            'output': {
                'processed_data_dir': str(temp_path / "processed"),
                'train_ratio': 0.7,
                'val_ratio': 0.15,
                'test_ratio': 0.15
            },
            'temp': {
                'temp_dir': str(temp_path / "temp"),
                'cleanup_temp': True
            },
            'processing': {
                'max_sessions_per_pcap': 10,
                'min_packets_per_session': 5,
                'image_size': 32,
                'sequence_length': 512,
                'graph_max_nodes': 100,
                'max_file_size_mb': 100
            },
            'parallel': {
                'num_workers': 1,
                'batch_size': 5
            },
            'validation': {
                'check_corrupted_files': False,
                'min_file_size_bytes': 0,
                'max_processing_time_minutes': 30
            }
        }
        
        pipeline = AutomatedPreprocessingPipeline()
        pipeline.config = config
        pipeline.input_dir = raw_data_dir
        pipeline.output_dir = temp_path / "processed"
        pipeline.temp_dir = temp_path / "temp"
        
        # 测试文件验证
        valid_files, errors = pipeline.validate_input_data()
        print(f"✅ 找到 {len(valid_files)} 个有效文件")
        print(f"   错误: {len(errors)} 个")
        
        # 测试文件组织
        class_files = pipeline.organize_files_by_class(valid_files)
        print(f"✅ 组织了 {len(class_files)} 个类别")
        
        for class_name, files in class_files.items():
            print(f"   - {class_name}: {len(files)} 个文件")
            assert len(files) == 1, f"每个类别应该只有一个PCAP文件，但 {class_name} 有 {len(files)} 个"
        
        print("✅ 扁平文件结构组织测试通过")


def test_config_validation():
    """
    测试配置验证
    """
    print("\n🧪 测试配置验证...")
    
    # 测试默认配置
    pipeline = AutomatedPreprocessingPipeline()
    assert pipeline.config is not None
    print("✅ 默认配置加载成功")
    
    # 测试配置参数
    required_sections = ['input', 'output', 'temp', 'processing', 'validation']
    for section in required_sections:
        assert section in pipeline.config, f"缺少配置节: {section}"
    
    print("✅ 配置验证测试通过")


def test_path_handling():
    """
    测试路径处理
    """
    print("\n🧪 测试路径处理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试文件
        test_files = [
            "normal_traffic.pcap",
            "ddos-attack.pcap", 
            "port scan.pcap",
            "malware_sample.pcap"
        ]
        
        raw_dir = temp_path / "raw"
        raw_dir.mkdir()
        
        for filename in test_files:
            (raw_dir / filename).touch()
        
        # 测试文件名处理
        pipeline = AutomatedPreprocessingPipeline()
        pipeline.input_dir = raw_dir
        
        valid_files = list(raw_dir.glob("*.pcap"))
        class_files = pipeline.organize_files_by_class(valid_files)
        
        # 验证类别名称处理
        expected_classes = ["normal_traffic", "ddos_attack", "port_scan", "malware_sample"]
        actual_classes = list(class_files.keys())
        
        print(f"期望类别: {expected_classes}")
        print(f"实际类别: {actual_classes}")
        
        for expected in expected_classes:
            # 处理特殊字符
            processed_name = expected.replace(' ', '_').replace('-', '_')
            assert processed_name in actual_classes, f"类别 {processed_name} 未找到"
        
        print("✅ 路径处理测试通过")


def test_error_handling():
    """
    测试错误处理
    """
    print("\n🧪 测试错误处理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 测试不存在的输入目录
        pipeline = AutomatedPreprocessingPipeline()
        pipeline.input_dir = temp_path / "nonexistent"
        
        valid_files, errors = pipeline.validate_input_data()
        assert len(valid_files) == 0, "不存在的目录应该返回0个有效文件"
        assert len(errors) > 0, "不存在的目录应该产生错误"
        
        print("✅ 错误处理测试通过")


def main():
    """
    运行所有测试
    """
    print("🚀 开始测试扁平文件结构预处理功能")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_config_validation()
        test_path_handling()
        test_flat_structure_organization()
        test_error_handling()
        
        print("\n🎉 所有测试通过!")
        print("✅ 扁平文件结构预处理功能正常工作")
        
        print("\n📋 使用说明:")
        print("1. 将PCAP文件按扁平结构组织:")
        print("   raw_data/")
        print("   ├── normal.pcap")
        print("   ├── ddos.pcap")
        print("   └── malware.pcap")
        print("")
        print("2. 运行预处理:")
        print("   python scripts/preprocess_data.py --input raw_data --output processed_data")
        print("")
        print("3. 系统将自动:")
        print("   - 从每个PCAP文件提取多个会话")
        print("   - 在会话级别分割训练/验证/测试集")
        print("   - 生成多模态数据（序列+图像+图）")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
