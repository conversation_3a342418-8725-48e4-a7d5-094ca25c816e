#!/usr/bin/env python3
"""
简化的数据预处理脚本 - 用于快速处理PCAP数据
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.parent
sys.path.append(str(current_dir))

from data.automated_preprocessing import AutomatedPreprocessingPipeline


def main():
    """
    主函数 - 简化的命令行接口
    """
    parser = argparse.ArgumentParser(
        description='自动化PCAP数据预处理 - 生成多模态训练数据',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本用法
  python scripts/preprocess_data.py --input ./raw_data --output ./processed_data
  
  # 使用自定义配置
  python scripts/preprocess_data.py --input ./raw_data --output ./processed_data --config config/preprocessing_config.yaml
  
  # 保留临时文件用于调试
  python scripts/preprocess_data.py --input ./raw_data --output ./processed_data --no-cleanup
  
  # 使用更多并行处理
  python scripts/preprocess_data.py --input ./raw_data --output ./processed_data --workers 8

输入数据结构:
  raw_data/
  ├── class1/
  │   ├── sample1.pcap
  │   ├── sample2.pcap
  │   └── ...
  └── class2/
      ├── sample3.pcap
      └── ...

输出数据结构:
  processed_data/
  ├── train/
  │   ├── class1/
  │   │   ├── sample1_session_001.pkl
  │   │   ├── sample1_session_001.png
  │   │   └── ...
  │   └── class2/
  ├── val/
  └── test/
        """
    )
    
    # 必需参数
    parser.add_argument(
        '--input', '-i', 
        type=str, 
        required=True, 
        help='原始PCAP数据目录路径'
    )
    
    parser.add_argument(
        '--output', '-o', 
        type=str, 
        required=True, 
        help='处理后数据输出目录路径'
    )
    
    # 可选参数
    parser.add_argument(
        '--config', '-c', 
        type=str, 
        default=None,
        help='预处理配置文件路径 (默认使用内置配置)'
    )
    
    parser.add_argument(
        '--temp', '-t', 
        type=str, 
        default='./temp_preprocessing',
        help='临时文件目录 (默认: ./temp_preprocessing)'
    )
    
    parser.add_argument(
        '--workers', '-w', 
        type=int, 
        default=4,
        help='并行处理数量 (默认: 4)'
    )
    
    parser.add_argument(
        '--no-cleanup', 
        action='store_true',
        help='不清理临时文件 (用于调试)'
    )
    
    # 数据分割参数
    parser.add_argument(
        '--train-ratio', 
        type=float, 
        default=0.7,
        help='训练集比例 (默认: 0.7)'
    )
    
    parser.add_argument(
        '--val-ratio', 
        type=float, 
        default=0.15,
        help='验证集比例 (默认: 0.15)'
    )
    
    parser.add_argument(
        '--test-ratio', 
        type=float, 
        default=0.15,
        help='测试集比例 (默认: 0.15)'
    )
    
    # 处理参数
    parser.add_argument(
        '--max-sessions', 
        type=int, 
        default=100000,
        help='每个PCAP文件最大会话数 (默认: 100000)'
    )
    
    parser.add_argument(
        '--min-packets', 
        type=int, 
        default=1,
        help='每个会话最小包数 (默认: 1)'
    )
    
    parser.add_argument(
        '--image-size', 
        type=int, 
        default=32,
        help='RGB图像尺寸 (默认: 32x32)'
    )
    
    # 调试参数
    parser.add_argument(
        '--verbose', '-v', 
        action='store_true',
        help='详细输出'
    )
    
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='只验证输入数据，不实际处理'
    )
    
    args = parser.parse_args()
    
    # 验证参数
    if not os.path.exists(args.input):
        print(f"❌ 错误: 输入目录不存在: {args.input}")
        return 1
    
    # 验证数据分割比例
    total_ratio = args.train_ratio + args.val_ratio + args.test_ratio
    if abs(total_ratio - 1.0) > 0.01:
        print(f"❌ 错误: 数据分割比例总和应为1.0，当前为: {total_ratio}")
        return 1
    
    # 打印配置信息
    print("🚀 自动化PCAP数据预处理")
    print("=" * 50)
    print(f"输入目录: {args.input}")
    print(f"输出目录: {args.output}")
    print(f"临时目录: {args.temp}")
    print(f"配置文件: {args.config or '使用默认配置'}")
    print(f"并行数量: {args.workers}")
    print(f"数据分割: 训练{args.train_ratio:.1%}, 验证{args.val_ratio:.1%}, 测试{args.test_ratio:.1%}")
    print(f"图像尺寸: {args.image_size}x{args.image_size}")
    print(f"清理临时文件: {'否' if args.no_cleanup else '是'}")
    print("=" * 50)
    
    try:
        # 创建预处理管道
        pipeline = AutomatedPreprocessingPipeline(args.config)
        
        # 更新配置
        pipeline.config['input']['raw_pcap_dir'] = args.input
        pipeline.config['output']['processed_data_dir'] = args.output
        pipeline.config['temp']['temp_dir'] = args.temp
        pipeline.config['parallel']['num_workers'] = args.workers
        pipeline.config['temp']['cleanup_temp'] = not args.no_cleanup
        
        # 更新数据分割比例
        pipeline.config['output']['train_ratio'] = args.train_ratio
        pipeline.config['output']['val_ratio'] = args.val_ratio
        pipeline.config['output']['test_ratio'] = args.test_ratio
        
        # 更新处理参数
        pipeline.config['processing']['max_sessions_per_pcap'] = args.max_sessions
        pipeline.config['processing']['min_packets_per_session'] = args.min_packets
        pipeline.config['processing']['image_size'] = args.image_size
        
        # 重新初始化路径
        pipeline.input_dir = Path(args.input)
        pipeline.output_dir = Path(args.output)
        pipeline.temp_dir = Path(args.temp)
        
        # 如果是dry run，只验证数据
        if args.dry_run:
            print("\n🔍 验证模式 - 只检查输入数据...")
            valid_files, errors = pipeline.validate_input_data()
            
            if valid_files:
                print(f"✅ 找到 {len(valid_files)} 个有效PCAP文件")
                class_files = pipeline.organize_files_by_class(valid_files)
                print(f"✅ 发现 {len(class_files)} 个类别")
                
                for class_name, files in class_files.items():
                    print(f"   - {class_name}: {len(files)} 个文件")
            
            if errors:
                print(f"⚠️ 发现 {len(errors)} 个错误:")
                for error in errors[:5]:  # 只显示前5个错误
                    print(f"   - {error}")
                if len(errors) > 5:
                    print(f"   ... 还有 {len(errors) - 5} 个错误")
            
            print("\n✅ 验证完成")
            return 0
        
        # 运行完整管道
        success = pipeline.run_full_pipeline()
        
        if success:
            print("\n🎉 预处理完成!")
            print(f"✅ 处理后的数据已保存到: {args.output}")
            print("\n📋 下一步:")
            print("1. 检查生成的数据集信息:")
            print(f"   cat {args.output}/dataset_info.yaml")
            print("\n2. 开始训练多模态模型:")
            print("   cd MultiModalProtoSimCLR")
            print("   python experiments/train_multimodal.py --config config/multimodal_config.yaml")
            print("\n3. 或者先测试数据加载:")
            print("   python test_multimodal_system.py")
            
            return 0
        else:
            print("\n❌ 预处理失败")
            print("请检查日志文件获取详细错误信息")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
        return 1
    except Exception as e:
        print(f"\n❌ 预处理过程中发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
