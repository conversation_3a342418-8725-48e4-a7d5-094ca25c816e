"""
注意力融合模块 - 使用多头注意力机制融合多模态特征
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional
import math


class MultiHeadAttention(nn.Module):
    """
    多头注意力机制
    """
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        """
        初始化多头注意力
        
        Args:
            d_model: 模型维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super(MultiHeadAttention, self).__init__()
        
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        # 线性变换层
        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化权重
        """
        for module in [self.W_q, self.W_k, self.W_v, self.W_o]:
            nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
    
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            query: 查询张量 [batch_size, seq_len_q, d_model]
            key: 键张量 [batch_size, seq_len_k, d_model]
            value: 值张量 [batch_size, seq_len_v, d_model]
            mask: 注意力掩码 [batch_size, seq_len_q, seq_len_k]
            
        Returns:
            注意力输出 [batch_size, seq_len_q, d_model]
        """
        batch_size = query.size(0)
        
        # 线性变换
        Q = self.W_q(query)  # [batch_size, seq_len_q, d_model]
        K = self.W_k(key)    # [batch_size, seq_len_k, d_model]
        V = self.W_v(value)  # [batch_size, seq_len_v, d_model]
        
        # 重塑为多头
        Q = Q.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)  # [batch_size, num_heads, seq_len_q, d_k]
        K = K.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)  # [batch_size, num_heads, seq_len_k, d_k]
        V = V.view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)  # [batch_size, num_heads, seq_len_v, d_k]
        
        # 计算注意力
        attention_output, attention_weights = self._scaled_dot_product_attention(Q, K, V, mask)
        
        # 合并多头
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model
        )  # [batch_size, seq_len_q, d_model]
        
        # 输出投影
        output = self.W_o(attention_output)
        
        return output
    
    def _scaled_dot_product_attention(self, Q: torch.Tensor, K: torch.Tensor, V: torch.Tensor,
                                    mask: Optional[torch.Tensor] = None) -> tuple:
        """
        缩放点积注意力
        
        Args:
            Q: 查询张量 [batch_size, num_heads, seq_len_q, d_k]
            K: 键张量 [batch_size, num_heads, seq_len_k, d_k]
            V: 值张量 [batch_size, num_heads, seq_len_v, d_k]
            mask: 注意力掩码
            
        Returns:
            注意力输出和权重
        """
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        # 应用掩码
        if mask is not None:
            mask = mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            scores = scores.masked_fill(mask == 0, float('-inf'))
        
        # Softmax归一化
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 计算注意力输出
        attention_output = torch.matmul(attention_weights, V)
        
        return attention_output, attention_weights


class CrossModalAttention(nn.Module):
    """
    跨模态注意力模块
    """
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        """
        初始化跨模态注意力
        
        Args:
            d_model: 模型维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super(CrossModalAttention, self).__init__()
        
        self.attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 4, d_model)
        )
        
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, query_modal: torch.Tensor, key_value_modal: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            query_modal: 查询模态特征 [batch_size, 1, d_model]
            key_value_modal: 键值模态特征 [batch_size, 1, d_model]
            
        Returns:
            融合后的特征 [batch_size, 1, d_model]
        """
        # 跨模态注意力
        attended = self.attention(query_modal, key_value_modal, key_value_modal)
        
        # 残差连接和层归一化
        output = self.norm1(query_modal + self.dropout(attended))
        
        # 前馈网络
        ffn_output = self.ffn(output)
        output = self.norm2(output + self.dropout(ffn_output))
        
        return output


class AttentionFusion(nn.Module):
    """
    基于注意力机制的多模态融合模块
    """
    
    def __init__(self, config: Dict):
        """
        初始化注意力融合模块
        
        Args:
            config: 配置字典，包含：
                - fusion_dim: 融合特征维度
                - attention_heads: 注意力头数
                - dropout: Dropout率
                - output_dim: 输出维度
        """
        super(AttentionFusion, self).__init__()
        
        # 从配置中提取参数
        self.fusion_dim = config.get('fusion_dim', 768)
        self.attention_heads = config.get('attention_heads', 8)
        self.dropout = config.get('dropout', 0.1)
        self.output_dim = config.get('output_dim', 512)
        
        # 模态特征投影层（统一维度）
        self.sequence_projection = nn.Linear(256, self.fusion_dim)  # 序列特征投影
        self.image_projection = nn.Linear(256, self.fusion_dim)     # 图像特征投影
        self.graph_projection = nn.Linear(256, self.fusion_dim)     # 图特征投影
        
        # 跨模态注意力层
        self.seq_to_img_attention = CrossModalAttention(self.fusion_dim, self.attention_heads, self.dropout)
        self.seq_to_graph_attention = CrossModalAttention(self.fusion_dim, self.attention_heads, self.dropout)
        self.img_to_seq_attention = CrossModalAttention(self.fusion_dim, self.attention_heads, self.dropout)
        self.img_to_graph_attention = CrossModalAttention(self.fusion_dim, self.attention_heads, self.dropout)
        self.graph_to_seq_attention = CrossModalAttention(self.fusion_dim, self.attention_heads, self.dropout)
        self.graph_to_img_attention = CrossModalAttention(self.fusion_dim, self.attention_heads, self.dropout)
        
        # 自注意力层（模态内注意力）
        self.self_attention = MultiHeadAttention(self.fusion_dim, self.attention_heads, self.dropout)
        
        # 融合权重学习
        self.fusion_weights = nn.Parameter(torch.ones(3) / 3)  # 初始化为均等权重
        
        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Linear(self.fusion_dim, self.fusion_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.fusion_dim // 2, self.output_dim),
            nn.LayerNorm(self.output_dim)
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(self.fusion_dim)
    
    def forward(self, sequence_features: torch.Tensor, image_features: torch.Tensor, 
                graph_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            sequence_features: 序列特征 [batch_size, seq_dim]
            image_features: 图像特征 [batch_size, img_dim]
            graph_features: 图特征 [batch_size, graph_dim]
            
        Returns:
            融合后的特征 [batch_size, output_dim]
        """
        batch_size = sequence_features.size(0)
        
        # 特征投影到统一维度
        seq_proj = self.sequence_projection(sequence_features).unsqueeze(1)  # [batch_size, 1, fusion_dim]
        img_proj = self.image_projection(image_features).unsqueeze(1)        # [batch_size, 1, fusion_dim]
        graph_proj = self.graph_projection(graph_features).unsqueeze(1)      # [batch_size, 1, fusion_dim]
        
        # 跨模态注意力融合
        # 序列模态增强
        seq_enhanced = seq_proj
        seq_enhanced = seq_enhanced + self.seq_to_img_attention(seq_proj, img_proj)
        seq_enhanced = seq_enhanced + self.seq_to_graph_attention(seq_proj, graph_proj)
        
        # 图像模态增强
        img_enhanced = img_proj
        img_enhanced = img_enhanced + self.img_to_seq_attention(img_proj, seq_proj)
        img_enhanced = img_enhanced + self.img_to_graph_attention(img_proj, graph_proj)
        
        # 图模态增强
        graph_enhanced = graph_proj
        graph_enhanced = graph_enhanced + self.graph_to_seq_attention(graph_proj, seq_proj)
        graph_enhanced = graph_enhanced + self.graph_to_img_attention(graph_proj, img_proj)
        
        # 层归一化
        seq_enhanced = self.layer_norm(seq_enhanced)
        img_enhanced = self.layer_norm(img_enhanced)
        graph_enhanced = self.layer_norm(graph_enhanced)
        
        # 拼接所有模态特征
        all_features = torch.cat([seq_enhanced, img_enhanced, graph_enhanced], dim=1)  # [batch_size, 3, fusion_dim]
        
        # 自注意力融合
        fused_features = self.self_attention(all_features, all_features, all_features)  # [batch_size, 3, fusion_dim]
        
        # 加权融合
        weights = F.softmax(self.fusion_weights, dim=0)
        weighted_features = fused_features * weights.view(1, 3, 1)
        final_features = weighted_features.sum(dim=1)  # [batch_size, fusion_dim]
        
        # 输出投影
        output = self.output_projection(final_features)
        
        return output
    
    def get_attention_weights(self) -> torch.Tensor:
        """
        获取融合权重（用于可视化）
        
        Returns:
            归一化的融合权重 [3]
        """
        return F.softmax(self.fusion_weights, dim=0)
    
    def get_cross_modal_attention_maps(self, sequence_features: torch.Tensor, 
                                     image_features: torch.Tensor, 
                                     graph_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        获取跨模态注意力图（用于可视化）
        
        Args:
            sequence_features: 序列特征
            image_features: 图像特征
            graph_features: 图特征
            
        Returns:
            注意力图字典
        """
        # 特征投影
        seq_proj = self.sequence_projection(sequence_features).unsqueeze(1)
        img_proj = self.image_projection(image_features).unsqueeze(1)
        graph_proj = self.graph_projection(graph_features).unsqueeze(1)
        
        attention_maps = {}
        
        # 这里需要修改注意力模块以返回注意力权重
        # 为简化实现，暂时返回空字典
        
        return attention_maps


if __name__ == "__main__":
    # 测试代码
    config = {
        'fusion_dim': 768,
        'attention_heads': 8,
        'dropout': 0.1,
        'output_dim': 512
    }
    
    fusion_module = AttentionFusion(config)
    
    # 测试数据
    batch_size = 4
    sequence_features = torch.randn(batch_size, 256)
    image_features = torch.randn(batch_size, 256)
    graph_features = torch.randn(batch_size, 256)
    
    # 前向传播
    fused_output = fusion_module(sequence_features, image_features, graph_features)
    print(f"Fused output shape: {fused_output.shape}")
    
    # 获取融合权重
    weights = fusion_module.get_attention_weights()
    print(f"Fusion weights: {weights}")
    print(f"Sequence weight: {weights[0]:.3f}")
    print(f"Image weight: {weights[1]:.3f}")
    print(f"Graph weight: {weights[2]:.3f}")
