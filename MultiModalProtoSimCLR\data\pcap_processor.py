"""
PCAP文件处理器 - 从PCAP文件中提取序列和图数据
"""

import os
import json
import pickle
import numpy as np
from typing import Dict, List, Tuple, Optional
import scapy.all as scapy
from collections import defaultdict
import networkx as nx


class PCAPProcessor:
    """
    PCAP文件处理器，用于从PCAP文件中提取多模态数据
    """
    
    def __init__(self, config: Dict):
        """
        初始化PCAP处理器
        
        Args:
            config: 配置字典，包含处理参数
        """
        self.config = config
        self.sequence_length = config.get('sequence_length', 512)
        self.graph_max_nodes = config.get('graph_max_nodes', 100)
        
        # 协议映射字典
        self.protocol_map = {
            'TCP': 1, 'UDP': 2, 'ICMP': 3, 'HTTP': 4, 'HTTPS': 5,
            'DNS': 6, 'FTP': 7, 'SSH': 8, 'TELNET': 9, 'SMTP': 10
        }
        
        # 端口到协议的映射
        self.port_protocol_map = {
            80: 'HTTP', 443: 'HTTPS', 53: 'DNS', 21: 'FTP',
            22: 'SSH', 23: 'TELNET', 25: 'SMTP'
        }
    
    def process_pcap_file(self, pcap_path: str) -> Dict:
        """
        处理单个PCAP文件，提取序列和图数据
        
        Args:
            pcap_path: PCAP文件路径
            
        Returns:
            包含序列数据和图数据的字典
        """
        try:
            # 读取PCAP文件
            packets = scapy.rdpcap(pcap_path)
            
            # 提取序列数据
            sequence_data = self._extract_sequence_data(packets)
            
            # 提取图数据
            graph_data = self._extract_graph_data(packets)
            
            return {
                'sequence': sequence_data,
                'graph': graph_data,
                'packet_count': len(packets),
                'file_path': pcap_path
            }
            
        except Exception as e:
            print(f"Error processing {pcap_path}: {e}")
            return None
    
    def _extract_sequence_data(self, packets) -> Dict:
        """
        从数据包中提取序列数据
        
        Args:
            packets: scapy数据包列表
            
        Returns:
            序列数据字典
        """
        sequence = []
        timestamps = []
        packet_sizes = []
        
        for packet in packets[:self.sequence_length]:
            # 提取基本特征
            features = self._extract_packet_features(packet)
            sequence.append(features)
            
            # 提取时间戳
            timestamps.append(float(packet.time))
            
            # 提取包大小
            packet_sizes.append(len(packet))
        
        # 填充或截断到固定长度
        sequence = self._pad_or_truncate(sequence, self.sequence_length)
        timestamps = self._pad_or_truncate(timestamps, self.sequence_length, pad_value=0.0)
        packet_sizes = self._pad_or_truncate(packet_sizes, self.sequence_length, pad_value=0)
        
        return {
            'features': np.array(sequence, dtype=np.float32),
            'timestamps': np.array(timestamps, dtype=np.float32),
            'packet_sizes': np.array(packet_sizes, dtype=np.int32),
            'length': min(len(packets), self.sequence_length)
        }
    
    def _extract_packet_features(self, packet) -> List[float]:
        """
        从单个数据包中提取特征向量
        
        Args:
            packet: scapy数据包对象
            
        Returns:
            特征向量列表
        """
        features = []
        
        # 基本特征
        features.append(len(packet))  # 包大小
        
        # 协议特征
        if packet.haslayer(scapy.IP):
            ip_layer = packet[scapy.IP]
            features.extend([
                ip_layer.proto,  # 协议类型
                len(ip_layer.src.split('.')),  # IP地址特征
                len(ip_layer.dst.split('.'))
            ])
        else:
            features.extend([0, 0, 0])
        
        # 传输层特征
        if packet.haslayer(scapy.TCP):
            tcp_layer = packet[scapy.TCP]
            features.extend([
                tcp_layer.sport,  # 源端口
                tcp_layer.dport,  # 目标端口
                tcp_layer.flags,  # TCP标志
                len(tcp_layer.payload) if tcp_layer.payload else 0
            ])
        elif packet.haslayer(scapy.UDP):
            udp_layer = packet[scapy.UDP]
            features.extend([
                udp_layer.sport,
                udp_layer.dport,
                0,  # UDP没有flags
                len(udp_layer.payload) if udp_layer.payload else 0
            ])
        else:
            features.extend([0, 0, 0, 0])
        
        # 应用层协议识别
        protocol_id = self._identify_protocol(packet)
        features.append(protocol_id)
        
        return features
    
    def _identify_protocol(self, packet) -> int:
        """
        识别应用层协议
        
        Args:
            packet: scapy数据包对象
            
        Returns:
            协议ID
        """
        if packet.haslayer(scapy.TCP) or packet.haslayer(scapy.UDP):
            layer = packet[scapy.TCP] if packet.haslayer(scapy.TCP) else packet[scapy.UDP]
            
            # 根据端口识别协议
            for port in [layer.sport, layer.dport]:
                if port in self.port_protocol_map:
                    protocol = self.port_protocol_map[port]
                    return self.protocol_map.get(protocol, 0)
        
        # 根据协议类型识别
        if packet.haslayer(scapy.TCP):
            return self.protocol_map.get('TCP', 1)
        elif packet.haslayer(scapy.UDP):
            return self.protocol_map.get('UDP', 2)
        elif packet.haslayer(scapy.ICMP):
            return self.protocol_map.get('ICMP', 3)
        
        return 0  # 未知协议
    
    def _extract_graph_data(self, packets) -> Dict:
        """
        从数据包中提取图数据
        
        Args:
            packets: scapy数据包列表
            
        Returns:
            图数据字典
        """
        # 构建网络图
        G = nx.Graph()
        ip_connections = defaultdict(int)
        
        for packet in packets:
            if packet.haslayer(scapy.IP):
                ip_layer = packet[scapy.IP]
                src_ip = ip_layer.src
                dst_ip = ip_layer.dst
                
                # 添加节点
                G.add_node(src_ip)
                G.add_node(dst_ip)
                
                # 添加边（累计连接次数）
                if G.has_edge(src_ip, dst_ip):
                    G[src_ip][dst_ip]['weight'] += 1
                else:
                    G.add_edge(src_ip, dst_ip, weight=1)
                
                ip_connections[(src_ip, dst_ip)] += 1
        
        # 限制节点数量
        if len(G.nodes()) > self.graph_max_nodes:
            # 按度数排序，保留最重要的节点
            node_degrees = dict(G.degree())
            top_nodes = sorted(node_degrees.items(), key=lambda x: x[1], reverse=True)
            top_nodes = [node for node, _ in top_nodes[:self.graph_max_nodes]]
            G = G.subgraph(top_nodes).copy()
        
        # 转换为数值表示
        node_mapping = {node: i for i, node in enumerate(G.nodes())}
        
        # 构建邻接矩阵
        adj_matrix = nx.adjacency_matrix(G, nodelist=list(node_mapping.keys())).toarray()
        
        # 构建节点特征矩阵
        node_features = self._extract_node_features(G, packets)
        
        # 构建边索引和边特征
        edge_index, edge_features = self._extract_edge_features(G, node_mapping)
        
        return {
            'adj_matrix': adj_matrix.astype(np.float32),
            'node_features': node_features.astype(np.float32),
            'edge_index': edge_index.astype(np.int64),
            'edge_features': edge_features.astype(np.float32),
            'num_nodes': len(G.nodes()),
            'num_edges': len(G.edges()),
            'node_mapping': node_mapping
        }
    
    def _extract_node_features(self, G: nx.Graph, packets) -> np.ndarray:
        """
        提取节点特征
        
        Args:
            G: NetworkX图对象
            packets: 数据包列表
            
        Returns:
            节点特征矩阵
        """
        node_features = []
        
        for node in G.nodes():
            features = [
                G.degree(node),  # 度数
                len(list(G.neighbors(node))),  # 邻居数量
                nx.clustering(G, node),  # 聚类系数
            ]
            
            # 添加更多统计特征
            node_packets = [p for p in packets if p.haslayer(scapy.IP) and 
                          (p[scapy.IP].src == node or p[scapy.IP].dst == node)]
            
            features.extend([
                len(node_packets),  # 相关数据包数量
                sum(len(p) for p in node_packets),  # 总字节数
                np.mean([len(p) for p in node_packets]) if node_packets else 0,  # 平均包大小
            ])
            
            node_features.append(features)
        
        # 填充到固定维度
        max_nodes = self.graph_max_nodes
        if len(node_features) < max_nodes:
            padding = [[0] * len(node_features[0])] * (max_nodes - len(node_features))
            node_features.extend(padding)
        
        return np.array(node_features[:max_nodes])
    
    def _extract_edge_features(self, G: nx.Graph, node_mapping: Dict) -> Tuple[np.ndarray, np.ndarray]:
        """
        提取边特征
        
        Args:
            G: NetworkX图对象
            node_mapping: 节点映射字典
            
        Returns:
            边索引和边特征
        """
        edge_index = []
        edge_features = []
        
        for src, dst, data in G.edges(data=True):
            src_idx = node_mapping[src]
            dst_idx = node_mapping[dst]
            
            edge_index.append([src_idx, dst_idx])
            edge_index.append([dst_idx, src_idx])  # 无向图，添加反向边
            
            weight = data.get('weight', 1)
            edge_features.append([weight, 1.0])  # 权重和存在性
            edge_features.append([weight, 1.0])
        
        if not edge_index:
            edge_index = [[0, 0]]
            edge_features = [[0.0, 0.0]]
        
        return np.array(edge_index).T, np.array(edge_features)
    
    def _pad_or_truncate(self, data: List, target_length: int, pad_value=0) -> List:
        """
        填充或截断数据到目标长度
        
        Args:
            data: 输入数据列表
            target_length: 目标长度
            pad_value: 填充值
            
        Returns:
            处理后的数据列表
        """
        if len(data) >= target_length:
            return data[:target_length]
        else:
            if isinstance(pad_value, (int, float)):
                padding = [pad_value] * (target_length - len(data))
            else:
                padding = [pad_value for _ in range(target_length - len(data))]
            return data + padding
    
    def batch_process(self, pcap_dir: str, output_dir: str) -> None:
        """
        批量处理PCAP文件
        
        Args:
            pcap_dir: PCAP文件目录
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        
        pcap_files = [f for f in os.listdir(pcap_dir) if f.endswith('.pcap')]
        
        for pcap_file in pcap_files:
            pcap_path = os.path.join(pcap_dir, pcap_file)
            print(f"Processing {pcap_file}...")
            
            result = self.process_pcap_file(pcap_path)
            
            if result is not None:
                output_path = os.path.join(output_dir, f"{pcap_file}.pkl")
                with open(output_path, 'wb') as f:
                    pickle.dump(result, f)
                print(f"Saved to {output_path}")
            else:
                print(f"Failed to process {pcap_file}")


if __name__ == "__main__":
    # 测试代码
    config = {
        'sequence_length': 512,
        'graph_max_nodes': 100
    }
    
    processor = PCAPProcessor(config)
    
    # 处理单个文件示例
    # result = processor.process_pcap_file("example.pcap")
    # print(result)
    
    # 批量处理示例
    # processor.batch_process("/path/to/pcap/files", "/path/to/output")
