"""
拼接融合模块 - 简单的特征拼接融合策略
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional


class ConcatFusion(nn.Module):
    """
    基于特征拼接的多模态融合模块
    """
    
    def __init__(self, config: Dict):
        """
        初始化拼接融合模块
        
        Args:
            config: 配置字典，包含：
                - fusion_dim: 融合特征维度（拼接后的总维度）
                - dropout: Dropout率
                - output_dim: 输出维度
        """
        super(ConcatFusion, self).__init__()
        
        # 从配置中提取参数
        self.fusion_dim = config.get('fusion_dim', 768)  # 3 * 256 = 768
        self.dropout = config.get('dropout', 0.1)
        self.output_dim = config.get('output_dim', 512)
        
        # 输入特征维度（从配置中获取或使用默认值）
        self.input_dim = config.get('input_dim', 256)
        self.expected_concat_dim = 3 * self.input_dim  # 768
        
        # 特征标准化层
        self.sequence_norm = nn.LayerNorm(self.input_dim)
        self.image_norm = nn.LayerNorm(self.input_dim)
        self.graph_norm = nn.LayerNorm(self.input_dim)
        
        # 特征投影层（可选，用于调整各模态特征）
        self.sequence_projection = nn.Sequential(
            nn.Linear(self.input_dim, self.input_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )
        
        self.image_projection = nn.Sequential(
            nn.Linear(self.input_dim, self.input_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )
        
        self.graph_projection = nn.Sequential(
            nn.Linear(self.input_dim, self.input_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )
        
        # 融合后的处理网络
        self.fusion_network = nn.Sequential(
            nn.Linear(self.expected_concat_dim, self.fusion_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.LayerNorm(self.fusion_dim),
            
            nn.Linear(self.fusion_dim, self.fusion_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.LayerNorm(self.fusion_dim // 2),
            
            nn.Linear(self.fusion_dim // 2, self.output_dim),
            nn.LayerNorm(self.output_dim)
        )
        
        # 模态权重（可学习的权重，用于加权拼接）
        self.modal_weights = nn.Parameter(torch.ones(3) / 3)  # 初始化为均等权重
        
        # 残差连接
        self.use_residual = config.get('use_residual', True)
        if self.use_residual:
            self.residual_projection = nn.Linear(self.input_dim, self.output_dim)
        
        self._init_weights()
    
    def _init_weights(self):
        """
        初始化权重
        """
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, sequence_features: torch.Tensor, image_features: torch.Tensor, 
                graph_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            sequence_features: 序列特征 [batch_size, input_dim]
            image_features: 图像特征 [batch_size, input_dim]
            graph_features: 图特征 [batch_size, input_dim]
            
        Returns:
            融合后的特征 [batch_size, output_dim]
        """
        batch_size = sequence_features.size(0)
        
        # 特征标准化
        seq_norm = self.sequence_norm(sequence_features)
        img_norm = self.image_norm(image_features)
        graph_norm = self.graph_norm(graph_features)
        
        # 特征投影（可选的特征变换）
        seq_proj = self.sequence_projection(seq_norm)
        img_proj = self.image_projection(img_norm)
        graph_proj = self.graph_projection(graph_norm)
        
        # 加权拼接
        weights = F.softmax(self.modal_weights, dim=0)
        weighted_seq = weights[0] * seq_proj
        weighted_img = weights[1] * img_proj
        weighted_graph = weights[2] * graph_proj
        
        # 特征拼接
        concatenated_features = torch.cat([weighted_seq, weighted_img, weighted_graph], dim=-1)
        
        # 融合网络处理
        fused_features = self.fusion_network(concatenated_features)
        
        # 残差连接（可选）
        if self.use_residual:
            # 使用平均特征作为残差
            avg_features = (sequence_features + image_features + graph_features) / 3
            residual = self.residual_projection(avg_features)
            fused_features = fused_features + 0.1 * residual
        
        return fused_features
    
    def get_modal_weights(self) -> torch.Tensor:
        """
        获取模态权重
        
        Returns:
            归一化的模态权重 [3]
        """
        return F.softmax(self.modal_weights, dim=0)
    
    def forward_with_intermediate(self, sequence_features: torch.Tensor, image_features: torch.Tensor, 
                                graph_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        前向传播并返回中间结果（用于分析）
        
        Args:
            sequence_features: 序列特征
            image_features: 图像特征
            graph_features: 图特征
            
        Returns:
            包含中间结果的字典
        """
        # 特征标准化
        seq_norm = self.sequence_norm(sequence_features)
        img_norm = self.image_norm(image_features)
        graph_norm = self.graph_norm(graph_features)
        
        # 特征投影
        seq_proj = self.sequence_projection(seq_norm)
        img_proj = self.image_projection(img_norm)
        graph_proj = self.graph_projection(graph_norm)
        
        # 加权
        weights = F.softmax(self.modal_weights, dim=0)
        weighted_seq = weights[0] * seq_proj
        weighted_img = weights[1] * img_proj
        weighted_graph = weights[2] * graph_proj
        
        # 拼接
        concatenated_features = torch.cat([weighted_seq, weighted_img, weighted_graph], dim=-1)
        
        # 融合
        fused_features = self.fusion_network(concatenated_features)
        
        # 残差
        residual = None
        if self.use_residual:
            avg_features = (sequence_features + image_features + graph_features) / 3
            residual = self.residual_projection(avg_features)
            fused_features = fused_features + 0.1 * residual
        
        return {
            'normalized_features': {
                'sequence': seq_norm,
                'image': img_norm,
                'graph': graph_norm
            },
            'projected_features': {
                'sequence': seq_proj,
                'image': img_proj,
                'graph': graph_proj
            },
            'weighted_features': {
                'sequence': weighted_seq,
                'image': weighted_img,
                'graph': weighted_graph
            },
            'concatenated_features': concatenated_features,
            'modal_weights': weights,
            'residual': residual,
            'final_output': fused_features
        }


class SimpleConcatFusion(nn.Module):
    """
    最简单的拼接融合模块（无权重，直接拼接）
    """
    
    def __init__(self, config: Dict):
        """
        初始化简单拼接融合模块
        
        Args:
            config: 配置字典
        """
        super(SimpleConcatFusion, self).__init__()
        
        self.input_dim = 256
        self.concat_dim = 3 * self.input_dim  # 768
        self.output_dim = config.get('output_dim', 512)
        self.dropout = config.get('dropout', 0.1)
        
        # 简单的MLP网络
        self.mlp = nn.Sequential(
            nn.Linear(self.concat_dim, self.concat_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.concat_dim // 2, self.output_dim),
            nn.LayerNorm(self.output_dim)
        )
    
    def forward(self, sequence_features: torch.Tensor, image_features: torch.Tensor, 
                graph_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            sequence_features: 序列特征 [batch_size, input_dim]
            image_features: 图像特征 [batch_size, input_dim]
            graph_features: 图特征 [batch_size, input_dim]
            
        Returns:
            融合后的特征 [batch_size, output_dim]
        """
        # 直接拼接
        concatenated = torch.cat([sequence_features, image_features, graph_features], dim=-1)
        
        # MLP处理
        output = self.mlp(concatenated)
        
        return output


class WeightedConcatFusion(nn.Module):
    """
    加权拼接融合模块（固定权重）
    """
    
    def __init__(self, config: Dict):
        """
        初始化加权拼接融合模块
        
        Args:
            config: 配置字典，包含：
                - sequence_weight: 序列模态权重
                - image_weight: 图像模态权重
                - graph_weight: 图模态权重
                - output_dim: 输出维度
        """
        super(WeightedConcatFusion, self).__init__()
        
        self.input_dim = 256
        self.output_dim = config.get('output_dim', 512)
        self.dropout = config.get('dropout', 0.1)
        
        # 固定权重
        self.sequence_weight = config.get('sequence_weight', 1.0)
        self.image_weight = config.get('image_weight', 1.0)
        self.graph_weight = config.get('graph_weight', 1.0)
        
        # 归一化权重
        total_weight = self.sequence_weight + self.image_weight + self.graph_weight
        self.sequence_weight /= total_weight
        self.image_weight /= total_weight
        self.graph_weight /= total_weight
        
        # 处理网络
        self.fusion_network = nn.Sequential(
            nn.Linear(3 * self.input_dim, self.input_dim * 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.input_dim * 2, self.output_dim),
            nn.LayerNorm(self.output_dim)
        )
    
    def forward(self, sequence_features: torch.Tensor, image_features: torch.Tensor, 
                graph_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            sequence_features: 序列特征
            image_features: 图像特征
            graph_features: 图特征
            
        Returns:
            融合后的特征
        """
        # 加权
        weighted_seq = self.sequence_weight * sequence_features
        weighted_img = self.image_weight * image_features
        weighted_graph = self.graph_weight * graph_features
        
        # 拼接
        concatenated = torch.cat([weighted_seq, weighted_img, weighted_graph], dim=-1)
        
        # 融合
        output = self.fusion_network(concatenated)
        
        return output


if __name__ == "__main__":
    # 测试代码
    config = {
        'fusion_dim': 768,
        'dropout': 0.1,
        'output_dim': 512,
        'use_residual': True
    }
    
    # 测试标准拼接融合
    fusion_module = ConcatFusion(config)
    
    # 测试数据
    batch_size = 4
    sequence_features = torch.randn(batch_size, 256)
    image_features = torch.randn(batch_size, 256)
    graph_features = torch.randn(batch_size, 256)
    
    # 前向传播
    fused_output = fusion_module(sequence_features, image_features, graph_features)
    print(f"Fused output shape: {fused_output.shape}")
    
    # 获取模态权重
    weights = fusion_module.get_modal_weights()
    print(f"Modal weights: {weights}")
    
    # 测试中间结果
    intermediate_results = fusion_module.forward_with_intermediate(
        sequence_features, image_features, graph_features
    )
    print(f"Intermediate results keys: {intermediate_results.keys()}")
    print(f"Concatenated features shape: {intermediate_results['concatenated_features'].shape}")
    
    # 测试简单拼接融合
    simple_fusion = SimpleConcatFusion(config)
    simple_output = simple_fusion(sequence_features, image_features, graph_features)
    print(f"Simple fusion output shape: {simple_output.shape}")
    
    # 测试加权拼接融合
    weighted_config = {
        'sequence_weight': 0.4,
        'image_weight': 0.4,
        'graph_weight': 0.2,
        'output_dim': 512,
        'dropout': 0.1
    }
    weighted_fusion = WeightedConcatFusion(weighted_config)
    weighted_output = weighted_fusion(sequence_features, image_features, graph_features)
    print(f"Weighted fusion output shape: {weighted_output.shape}")
