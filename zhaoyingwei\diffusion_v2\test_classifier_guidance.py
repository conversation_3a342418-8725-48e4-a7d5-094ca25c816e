"""
Test script for Classifier Guidance functionality.
Tests classifier model, training logic, and CG sampling.
"""

import os
import sys
import tempfile
import torch as th
import numpy as np
from PIL import Image

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_classifier_model():
    """Test classifier model creation and forward pass"""
    print("🔍 Testing classifier model...")
    
    try:
        from diffusion_v2.classifier import Classifier
        
        # Create a small classifier
        classifier = Classifier(
            image_size=32,
            in_channels=3,
            model_channels=32,
            out_channels=3,  # 3 classes
            num_res_blocks=1,
            attention_resolutions="",
            channel_mult=(1, 2),
            num_heads=2,
        )
        
        # Test data
        batch_size = 2
        x = th.randn(batch_size, 3, 32, 32)
        timesteps = th.randint(0, 1000, (batch_size,))
        
        # Forward pass
        logits = classifier(x, timesteps)
        
        print(f"✅ Classifier forward pass successful")
        print(f"   Input shape: {x.shape}")
        print(f"   Timesteps shape: {timesteps.shape}")
        print(f"   Output logits shape: {logits.shape}")
        print(f"   Expected output shape: ({batch_size}, 3)")
        
        assert logits.shape == (batch_size, 3), f"Expected shape ({batch_size}, 3), got {logits.shape}"
        
        print("✅ Classifier model test passed")
        return True
        
    except Exception as e:
        print(f"❌ Classifier model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cond_fn():
    """Test classifier guidance function"""
    print("🔍 Testing classifier guidance function...")
    
    try:
        from diffusion_v2.classifier import Classifier
        from diffusion_v2.scripts.classifier_sample import cond_fn
        
        # Create classifier
        classifier = Classifier(
            image_size=32,
            in_channels=3,
            model_channels=32,
            out_channels=3,
            num_res_blocks=1,
            attention_resolutions="",
            channel_mult=(1, 2),
        )
        classifier.eval()
        
        # Test data
        batch_size = 2
        x = th.randn(batch_size, 3, 32, 32, requires_grad=True)
        t = th.randint(0, 1000, (batch_size,))
        y = th.tensor([0, 1])  # Target classes
        classifier_scale = 1.0
        
        # Test guidance function
        gradient = cond_fn(x, t, y, classifier, classifier_scale)
        
        print(f"✅ Classifier guidance function successful")
        print(f"   Input shape: {x.shape}")
        print(f"   Gradient shape: {gradient.shape}")
        print(f"   Gradient norm: {gradient.norm().item():.6f}")
        
        assert gradient.shape == x.shape, f"Expected gradient shape {x.shape}, got {gradient.shape}"
        assert gradient.requires_grad == False, "Gradient should not require grad"
        
        print("✅ Classifier guidance function test passed")
        return True
        
    except Exception as e:
        print(f"❌ Classifier guidance function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_diffusion_with_cond_fn():
    """Test diffusion sampling with cond_fn"""
    print("🔍 Testing diffusion sampling with cond_fn...")
    
    try:
        from diffusion_v2.gaussian_diffusion import GaussianDiffusion, ModelMeanType, ModelVarType, LossType
        from diffusion_v2.unet import UNetModel
        from diffusion_v2.classifier import Classifier
        
        # Create simple models
        model = UNetModel(
            in_channels=3,
            model_channels=32,
            out_channels=3,
            num_res_blocks=1,
            attention_resolutions="",
            num_classes=3,
        )
        
        classifier = Classifier(
            image_size=32,
            in_channels=3,
            model_channels=32,
            out_channels=3,
            num_res_blocks=1,
            attention_resolutions="",
            channel_mult=(1, 2),
        )
        
        # Create diffusion process
        betas = np.linspace(0.0001, 0.02, 100)
        diffusion = GaussianDiffusion(
            betas=betas,
            model_mean_type=ModelMeanType.EPSILON,
            model_var_type=ModelVarType.FIXED_SMALL,
            loss_type=LossType.MSE,
        )
        
        model.eval()
        classifier.eval()
        
        # Test single step sampling with cond_fn
        batch_size = 1
        x = th.randn(batch_size, 3, 32, 32)
        t = th.tensor([50])  # Mid-timestep
        y = th.tensor([1])   # Target class
        
        def test_cond_fn(x_in, t_in, **kwargs):
            # Simple mock guidance function
            return th.randn_like(x_in) * 0.1
        
        # Test p_mean_variance with cond_fn
        model_kwargs = {"y": y}
        out = diffusion.p_mean_variance(
            model, x, t, 
            model_kwargs=model_kwargs,
            cond_fn=test_cond_fn
        )
        
        print(f"✅ Diffusion with cond_fn successful")
        print(f"   Input shape: {x.shape}")
        print(f"   Output keys: {list(out.keys())}")
        print(f"   Mean shape: {out['mean'].shape}")
        
        assert "mean" in out, "Output should contain 'mean'"
        assert out["mean"].shape == x.shape, f"Expected mean shape {x.shape}, got {out['mean'].shape}"
        
        print("✅ Diffusion with cond_fn test passed")
        return True
        
    except Exception as e:
        print(f"❌ Diffusion with cond_fn test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_script_imports():
    """Test that all CG scripts can be imported"""
    print("🔍 Testing CG script imports...")
    
    scripts_to_test = [
        "diffusion_v2.classifier",
        "diffusion_v2.scripts.classifier_train",
        "diffusion_v2.scripts.classifier_sample",
        "diffusion_v2.scripts.train_class_conditional",
    ]
    
    success_count = 0
    
    for script in scripts_to_test:
        try:
            __import__(script)
            print(f"✅ Successfully imported {script}")
            success_count += 1
        except Exception as e:
            print(f"❌ Failed to import {script}: {e}")
    
    if success_count == len(scripts_to_test):
        print("✅ All CG script imports test passed")
        return True
    else:
        print(f"❌ {len(scripts_to_test) - success_count} imports failed")
        return False


def test_classifier_defaults():
    """Test classifier defaults and creation"""
    print("🔍 Testing classifier defaults and creation...")
    
    try:
        from diffusion_v2.script_util import classifier_defaults, create_classifier
        
        # Get defaults
        defaults = classifier_defaults()
        print(f"   Classifier defaults: {defaults}")
        
        # Test classifier creation
        classifier = create_classifier(**defaults)
        
        print(f"✅ Classifier creation successful")
        print(f"   Classifier type: {type(classifier).__name__}")
        print(f"   Image size: {classifier.image_size}")
        print(f"   Output classes: {classifier.out_channels}")
        
        # Test forward pass
        x = th.randn(1, 3, defaults["image_size"], defaults["image_size"])
        t = th.randint(0, 1000, (1,))
        logits = classifier(x, t)
        
        assert logits.shape == (1, defaults["num_classes"]), f"Expected shape (1, {defaults['num_classes']}), got {logits.shape}"
        
        print("✅ Classifier defaults and creation test passed")
        return True
        
    except Exception as e:
        print(f"❌ Classifier defaults and creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("🧪 Testing Classifier Guidance (CG) Features")
    print("=" * 60)
    
    tests = [
        ("CG Script Imports", test_script_imports),
        ("Classifier Model", test_classifier_model),
        ("Classifier Defaults", test_classifier_defaults),
        ("Classifier Guidance Function", test_cond_fn),
        ("Diffusion with cond_fn", test_diffusion_with_cond_fn),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All CG tests passed! Classifier Guidance is working correctly.")
        print("\n📋 Key features verified:")
        print("✅ Classifier model creation and forward pass")
        print("✅ Classifier guidance function (cond_fn)")
        print("✅ Diffusion sampling with cond_fn support")
        print("✅ Script imports and defaults")
        
        print("\n📝 Next steps:")
        print("1. Train a diffusion model using train_class_conditional.py")
        print("2. Train a classifier using classifier_train.py")
        print("3. Use trained models for CG sampling with classifier_sample.py")
        print("4. Experiment with different classifier scales")
    else:
        print("⚠️ Some tests failed. Please check the error messages above.")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
