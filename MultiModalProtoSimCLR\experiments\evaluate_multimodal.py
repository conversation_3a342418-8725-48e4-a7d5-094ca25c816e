"""
多模态模型评估脚本
"""

import os
import sys
import argparse
import yaml
import torch
import numpy as np
from pathlib import Path
from tqdm import tqdm
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from models.multimodal_protonet import MultiModalProtoNet
from data.multimodal_dataset import MultiModalFewShotDataset
from torch.utils.data import DataLoader


class MultiModalEvaluator:
    """
    多模态模型评估器
    """
    
    def __init__(self, config: dict, model: MultiModalProtoNet, device: str = 'cuda'):
        """
        初始化评估器
        
        Args:
            config: 配置字典
            model: 多模态原型网络模型
            device: 计算设备
        """
        self.config = config
        self.model = model
        self.device = torch.device(device)
        self.model.to(self.device)
        self.model.eval()
        
        # 评估参数
        self.n_way = config['protonet']['n_way']
        self.k_shot = config['protonet']['k_shot']
        self.k_query = config['protonet']['k_query']
        
        print(f"✅ Evaluator initialized")
        print(f"✅ Device: {self.device}")
        print(f"✅ N-way: {self.n_way}, K-shot: {self.k_shot}")
    
    def evaluate_few_shot(self, test_dataset: MultiModalFewShotDataset, 
                         num_episodes: int = 100) -> dict:
        """
        评估少样本学习性能
        
        Args:
            test_dataset: 测试数据集
            num_episodes: 测试episode数量
            
        Returns:
            评估结果字典
        """
        print(f"\n🧪 Evaluating few-shot performance on {num_episodes} episodes...")
        
        # 创建数据加载器
        test_loader = DataLoader(
            test_dataset,
            batch_size=1,
            shuffle=True,
            num_workers=4
        )
        
        all_predictions = []
        all_targets = []
        episode_accuracies = []
        
        with torch.no_grad():
            progress_bar = tqdm(enumerate(test_loader), total=num_episodes, desc='Evaluating')
            
            for episode_idx, (support_x, support_y, query_x, query_y) in progress_bar:
                if episode_idx >= num_episodes:
                    break
                
                # 移动数据到设备
                support_x = self._move_to_device(support_x)
                query_x = self._move_to_device(query_x)
                query_y = query_y.to(self.device)
                
                # 前向传播
                logits = self.model.episode_forward(support_x, query_x)
                
                # 预测
                predictions = logits.argmax(dim=1)
                
                # 计算episode准确率
                episode_acc = (predictions == query_y).float().mean().item()
                episode_accuracies.append(episode_acc)
                
                # 收集预测和真实标签
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(query_y.cpu().numpy())
                
                # 更新进度条
                progress_bar.set_postfix({'Acc': f'{episode_acc:.4f}'})
        
        # 计算总体指标
        overall_accuracy = accuracy_score(all_targets, all_predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(
            all_targets, all_predictions, average='macro'
        )
        
        # 计算置信区间
        episode_accuracies = np.array(episode_accuracies)
        mean_acc = episode_accuracies.mean()
        std_acc = episode_accuracies.std()
        ci_95 = 1.96 * std_acc / np.sqrt(len(episode_accuracies))
        
        results = {
            'overall_accuracy': overall_accuracy,
            'episode_mean_accuracy': mean_acc,
            'episode_std_accuracy': std_acc,
            'confidence_interval_95': ci_95,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'episode_accuracies': episode_accuracies,
            'predictions': all_predictions,
            'targets': all_targets
        }
        
        print(f"✅ Few-shot evaluation completed")
        print(f"   Overall Accuracy: {overall_accuracy:.4f}")
        print(f"   Episode Mean Accuracy: {mean_acc:.4f} ± {std_acc:.4f}")
        print(f"   95% Confidence Interval: ±{ci_95:.4f}")
        print(f"   Precision: {precision:.4f}")
        print(f"   Recall: {recall:.4f}")
        print(f"   F1-Score: {f1:.4f}")
        
        return results
    
    def evaluate_branch_contributions(self, test_dataset: MultiModalFewShotDataset,
                                    num_episodes: int = 50) -> dict:
        """
        评估各分支的贡献
        
        Args:
            test_dataset: 测试数据集
            num_episodes: 测试episode数量
            
        Returns:
            分支贡献分析结果
        """
        print(f"\n🧪 Analyzing branch contributions on {num_episodes} episodes...")
        
        test_loader = DataLoader(test_dataset, batch_size=1, shuffle=True, num_workers=4)
        
        branch_similarities = {'seq_img': [], 'seq_graph': [], 'img_graph': []}
        fusion_weights_history = []
        
        with torch.no_grad():
            progress_bar = tqdm(enumerate(test_loader), total=num_episodes, desc='Analyzing')
            
            for episode_idx, (support_x, support_y, query_x, query_y) in progress_bar:
                if episode_idx >= num_episodes:
                    break
                
                # 移动数据到设备
                support_x = self._move_to_device(support_x)
                query_x = self._move_to_device(query_x)
                
                # 分析支持集
                support_analysis = self.model.get_fusion_analysis(support_x)
                
                # 记录分支相似度
                for key, value in support_analysis['branch_similarities'].items():
                    branch_similarities[key].append(value)
                
                # 记录融合权重
                if 'fusion_weights' in support_analysis:
                    fusion_weights_history.append(support_analysis['fusion_weights'].cpu().numpy())
        
        # 计算平均值
        avg_similarities = {key: np.mean(values) for key, values in branch_similarities.items()}
        
        if fusion_weights_history:
            avg_fusion_weights = np.mean(fusion_weights_history, axis=0)
        else:
            avg_fusion_weights = None
        
        results = {
            'branch_similarities': avg_similarities,
            'fusion_weights': avg_fusion_weights,
            'similarity_history': branch_similarities,
            'fusion_weights_history': fusion_weights_history
        }
        
        print(f"✅ Branch contribution analysis completed")
        print(f"   Average branch similarities:")
        for key, value in avg_similarities.items():
            print(f"     {key}: {value:.4f}")
        
        if avg_fusion_weights is not None:
            print(f"   Average fusion weights: {avg_fusion_weights}")
        
        return results
    
    def _move_to_device(self, data):
        """
        将数据移动到指定设备
        """
        if isinstance(data, dict):
            return {key: self._move_to_device(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._move_to_device(item) for item in data]
        elif isinstance(data, torch.Tensor):
            return data.to(self.device)
        else:
            return data
    
    def plot_confusion_matrix(self, results: dict, save_path: str = None):
        """
        绘制混淆矩阵
        
        Args:
            results: 评估结果
            save_path: 保存路径
        """
        cm = confusion_matrix(results['targets'], results['predictions'])
        
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ Confusion matrix saved to {save_path}")
        
        plt.show()
    
    def plot_accuracy_distribution(self, results: dict, save_path: str = None):
        """
        绘制准确率分布
        
        Args:
            results: 评估结果
            save_path: 保存路径
        """
        accuracies = results['episode_accuracies']
        
        plt.figure(figsize=(10, 6))
        plt.hist(accuracies, bins=20, alpha=0.7, edgecolor='black')
        plt.axvline(accuracies.mean(), color='red', linestyle='--', 
                   label=f'Mean: {accuracies.mean():.4f}')
        plt.xlabel('Episode Accuracy')
        plt.ylabel('Frequency')
        plt.title('Distribution of Episode Accuracies')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ Accuracy distribution saved to {save_path}")
        
        plt.show()


def main():
    """
    主评估函数
    """
    parser = argparse.ArgumentParser(description='Multi-Modal ProtoSimCLR Evaluation')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--checkpoint', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--test_data', type=str, required=True, help='Path to test data')
    parser.add_argument('--episodes', type=int, default=100, help='Number of test episodes')
    parser.add_argument('--gpu', type=int, default=0, help='GPU device ID')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results', 
                       help='Output directory for results')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 设置设备
    device = f'cuda:{args.gpu}' if torch.cuda.is_available() else 'cpu'
    
    # 创建模型
    print("🚀 Creating model...")
    model = MultiModalProtoNet(config)
    
    # 加载检查点
    print(f"🚀 Loading checkpoint from {args.checkpoint}...")
    checkpoint = torch.load(args.checkpoint, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建评估器
    evaluator = MultiModalEvaluator(config, model, device)
    
    # 创建测试数据集
    print("🚀 Creating test dataset...")
    test_dataset = MultiModalFewShotDataset(
        config=config,
        data_root=args.test_data,
        split='test'
    )
    
    # 评估少样本学习性能
    results = evaluator.evaluate_few_shot(test_dataset, args.episodes)
    
    # 分析分支贡献
    branch_results = evaluator.evaluate_branch_contributions(test_dataset, args.episodes // 2)
    
    # 保存结果
    results_path = os.path.join(args.output_dir, 'evaluation_results.yaml')
    with open(results_path, 'w') as f:
        yaml.dump({
            'few_shot_results': {k: v for k, v in results.items() 
                               if k not in ['episode_accuracies', 'predictions', 'targets']},
            'branch_analysis': {k: v for k, v in branch_results.items() 
                              if k not in ['similarity_history', 'fusion_weights_history']}
        }, f)
    
    print(f"✅ Results saved to {results_path}")
    
    # 绘制图表
    evaluator.plot_confusion_matrix(
        results, 
        os.path.join(args.output_dir, 'confusion_matrix.png')
    )
    
    evaluator.plot_accuracy_distribution(
        results,
        os.path.join(args.output_dir, 'accuracy_distribution.png')
    )
    
    print("🎉 Evaluation completed!")


if __name__ == "__main__":
    main()
