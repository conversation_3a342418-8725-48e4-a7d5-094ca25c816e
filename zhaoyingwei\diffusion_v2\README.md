# Diffusion V2 - Enhanced Class-Conditional Training

This is an enhanced version of the improved_diffusion library with two key new features:

## 🆕 New Features

### 1. Custom Log Directory Support
- **Feature**: Specify custom directory for saving model weights and training logs
- **Usage**: `--log_dir /path/to/your/logs`
- **Benefits**: Better organization of experiments and easy access to checkpoints

### 2. Classifier Guidance (CG) Training
- **Feature**: Train separate classifier and diffusion models for guided generation
- **Usage**: Two-stage training process with independent classifier
- **Benefits**: High-quality conditional generation with flexible control

### 3. ImageFolder Dataset Support
- **Feature**: Automatic class label extraction from directory structure
- **Usage**: `--use_imagefolder True`
- **Benefits**: Easy to use with standard dataset formats

## 📁 Dataset Format

For ImageFolder format, organize your data like this:
```
dataset/
├── class1/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── class2/
│   ├── image1.jpg
│   └── ...
└── class3/
    └── ...
```

## 🚀 Quick Start

### Step 1: Train Diffusion Model
```bash
python scripts/train_class_conditional.py \
    --data_dir /path/to/your/dataset \
    --num_classes 10 \
    --log_dir ./logs/diffusion \
    --class_cond True \
    --use_imagefolder True \
    --image_size 64 \
    --batch_size 8 \
    --save_interval 1000
```

### Step 2: Train Classifier
```bash
python scripts/classifier_train.py \
    --data_dir /path/to/your/dataset \
    --num_classes 10 \
    --log_dir ./logs/classifier \
    --use_imagefolder True \
    --image_size 64 \
    --batch_size 32 \
    --iterations 100000
```

### Step 3: Sampling with CG
```bash
python scripts/classifier_sample.py \
    --model_path ./logs/diffusion/model010000.pt \
    --classifier_path ./logs/classifier/classifier_final.pt \
    --num_classes 10 \
    --class_cond True \
    --classifier_scale 1.0 \
    --num_samples 16 \
    --output_dir ./samples
```

## 📋 Key Parameters

### Training Parameters
- `--log_dir`: Custom directory for logs and checkpoints
- `--cfg_dropout_prob`: CFG dropout probability (0.0-1.0, default: 0.1)
- `--use_imagefolder`: Use ImageFolder format (default: True)
- `--num_classes`: Number of classes in your dataset
- `--class_cond`: Enable class conditioning (default: True)

### Sampling Parameters
- `--guidance_scale`: CFG guidance strength (1.0 = no guidance, 2.0-10.0 typical)
- `--specific_class`: Generate specific class (-1 for random)
- `--output_dir`: Directory to save generated samples

## 🔧 How CFG Works

1. **Training**: Randomly drop class labels with probability `cfg_dropout_prob`
2. **Sampling**: Generate both conditional and unconditional predictions
3. **Guidance**: Combine predictions using: `uncond + guidance_scale * (cond - uncond)`

## 📊 Guidance Scale Effects

- `1.0`: No guidance (standard conditional generation)
- `2.0-3.0`: Moderate guidance (good balance)
- `5.0-10.0`: Strong guidance (higher quality but less diversity)
- `>10.0`: Very strong guidance (may cause artifacts)

## 🎯 Example Workflow

1. **Prepare Dataset**: Organize images in ImageFolder format
2. **Train Model**: Use `train_class_conditional.py` with CFG enabled
3. **Generate Samples**: Use `image_sample_cfg.py` with different guidance scales
4. **Evaluate**: Compare samples with different guidance scales

## 📝 Example Usage Script

Run the interactive example:
```bash
python example_usage.py
```

This script will guide you through:
- Setting up your dataset path
- Configuring training parameters
- Running training and sampling commands

## 🔍 Monitoring Training

Training logs and checkpoints are saved to your specified `--log_dir`:
```
logs/experiment1/
├── model010000.pt      # Model checkpoint
├── ema_0.9999_010000.pt # EMA checkpoint
├── opt010000.pt        # Optimizer state
└── progress.txt        # Training logs
```

## 🎨 Sample Output

Generated samples will be saved as:
- Individual PNG files: `sample_000001_class_5.png`
- Numpy array: `samples.npz` (for evaluation)

## 🚨 Important Notes

1. **CFG Training**: Always use `cfg_dropout_prob > 0` for CFG-capable models
2. **Memory**: CFG sampling uses ~2x memory (conditional + unconditional forward passes)
3. **Classes**: Make sure `num_classes` matches your dataset
4. **ImageFolder**: Directory names become class labels (sorted alphabetically)

## 🔄 Migration from Original

If you have existing scripts, update them:
```python
# Old way
from improved_diffusion import ...

# New way  
from diffusion_v2 import ...
```

Add new parameters:
```bash
# Add these to your training command
--log_dir ./logs/my_experiment
--cfg_dropout_prob 0.1
--use_imagefolder True
```

## 🐛 Troubleshooting

**Issue**: "must specify y if and only if the model is class-conditional"
**Solution**: Make sure `--class_cond True` and your dataset has class labels

**Issue**: CFG sampling produces poor results
**Solution**: Ensure model was trained with `cfg_dropout_prob > 0`

**Issue**: Out of memory during sampling
**Solution**: Reduce `--batch_size` or `--guidance_scale`

## 📚 References

- [Classifier-Free Diffusion Guidance](https://arxiv.org/abs/2207.12598)
- [Denoising Diffusion Probabilistic Models](https://arxiv.org/abs/2006.11239)
- [Improved Denoising Diffusion Probabilistic Models](https://arxiv.org/abs/2102.09672)
