import os
import random
import shutil


def copy_random_images(source_dir, target_dir, n):
    """
    从源目录随机抽取n张图片复制到目标目录

    参数:
        source_dir (str): 源图片目录路径
        target_dir (str): 目标目录路径
        n (int): 需要抽取的图片数量
    """
    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)

    # 获取源目录下所有图片文件
    image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff')
    all_images = [
        f for f in os.listdir(source_dir)
        if f.lower().endswith(image_extensions) and os.path.isfile(os.path.join(source_dir, f))
    ]

    if not all_images:
        print(f"源目录 {source_dir} 中没有找到图片文件")
        return

    # 如果要抽取的数量大于可用图片数量，则调整n值
    n = min(n, len(all_images))

    # 随机选择n张图片
    selected_images = random.sample(all_images, n)

    # 复制选中的图片到目标目录
    for img in selected_images:
        src_path = os.path.join(source_dir, img)
        dst_path = os.path.join(target_dir, img)
        shutil.move(src_path, dst_path)
        print(f"已复制: {img}")

    print(f"已完成，共复制了 {n} 张图片到 {target_dir}")


# 使用示例
if __name__ == "__main__":
    source_directory = "./Htbot"  # 替换为你的源图片目录
    target_directory = "./Htbot_temp"  # 替换为目标目录
    number_to_copy = 50  # 要抽取的图片数量

    copy_random_images(source_directory, target_directory, number_to_copy)
